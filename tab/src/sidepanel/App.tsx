import React, { useState, useMemo, useRef } from 'react';
import { Plus, Loader2 } from 'lucide-react';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import WorkspaceList from '@/components/WorkspaceList';
import CreateWorkspaceModal from '@/components/CreateWorkspaceModal';
import SettingsPanel from '@/components/SettingsPanel';
import Header from '@/components/Header';
import ErrorBoundary from '@/components/ErrorBoundary';
import { ToastProvider, useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';
import { WorkonaTabManager } from '@/utils/workonaTabManager';
import { StorageManager } from '@/utils/storage';
import { AppSkeleton } from '@/components/SkeletonLoader';

/**
 * 内部应用组件（使用 Toast）
 */
const AppContent: React.FC = () => {
  const {
    workspaces,
    activeWorkspace,
    settings,
    loading,
    error,
    workspaceSetupStatus,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    switchWorkspace,
    addWebsite,
    addCurrentTabByWorkonaId,
    removeWebsite,
    updateWebsite,
    reorderWorkspaces,
    reorderWebsites,
    updateSettings,
    reload,
  } = useWorkspaces();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Toast 错误处理
  const { showError } = useToast();
  const errorHandler = useMemo(() => new ToastErrorHandler(showError), [showError]);

  // 用于跟踪已显示的错误，避免重复显示
  const lastShownErrorRef = useRef<string | null>(null);

  // 处理错误状态的Toast显示
  React.useEffect(() => {
    if (error && error !== lastShownErrorRef.current) {
      errorHandler.handle(error, '加载工作区');
      lastShownErrorRef.current = error;
    }
    // 当错误清除时，重置跟踪
    if (!error) {
      lastShownErrorRef.current = null;
    }
  }, [error, errorHandler]);

  /**
   * 处理创建工作区
   */
  const handleCreateWorkspace = async (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
    addCurrentTabs?: boolean;
  }) => {
    try {
      await createWorkspace(name, options);
      setShowCreateModal(false);
    } catch (err) {
      errorHandler.handleWorkspaceError(err, '创建');
    }
  };

  /**
   * 处理工作区切换
   */
  const handleSwitchWorkspace = async (workspaceId: string) => {
    try {
      await switchWorkspace(workspaceId);
    } catch (err) {
      errorHandler.handleWorkspaceError(err, '切换');
    }
  };

  /**
   * 处理添加当前标签页（基于Workona ID血缘关系）
   */
  const handleAddCurrentTab = async (workspaceId: string) => {
    try {
      // 获取当前活跃标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tab = tabs[0];
        if (tab.url && !tab.url.startsWith('chrome://') && tab.id) {
          // 使用基于Workona ID血缘关系的方法直接处理当前标签页
          await addCurrentTabByWorkonaId(workspaceId, tab.id, {
            url: tab.url,
            title: tab.title,
            favicon: tab.favIconUrl || undefined,
          });
        }
      }
    } catch (err) {
      errorHandler.handleWebsiteError(err, '添加当前标签页');
    }
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsiteUrl = async (workspaceId: string, url: string) => {
    try {
      await addWebsite(workspaceId, url, {
        openInNewTab: true,
      });
    } catch (err) {
      errorHandler.handleWebsiteError(err, '添加网站');
    }
  };

  /**
   * 处理更新网站
   */
  const handleUpdateWebsite = async (workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => {
    try {
      await updateWebsite(workspaceId, websiteId, updates);
    } catch (err) {
      errorHandler.handleWebsiteError(err, '更新网站');
    }
  };

  /**
   * 处理切换固定状态
   */
  const handleTogglePin = async (workspaceId: string, websiteId: string, isPinned: boolean) => {
    try {
      await updateWebsite(workspaceId, websiteId, { isPinned });
    } catch (err) {
      errorHandler.handleWebsiteError(err, '切换固定状态');
    }
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = async (workspaceId: string, websiteIds: string[], isPinned: boolean) => {
    try {
      // 批量更新网站的固定状态
      for (const websiteId of websiteIds) {
        await updateWebsite(workspaceId, websiteId, { isPinned });
      }
    } catch (err) {
      errorHandler.handleWebsiteError(err, '批量固定网站');
    }
  };

  /**
   * 处理批量删除（集成标签页降级机制）
   */
  const handleBatchDelete = async (workspaceId: string, websiteIds: string[]) => {
    try {
      console.log(`🗑️ 批量删除网站: ${websiteIds.length} 个`);

      // 在删除网站前，先处理相关的工作区专属标签页降级
      // 使用静态导入的 WorkonaTabManager 和 StorageManager

      for (const websiteId of websiteIds) {
        try {
          // 查找与该网站关联的工作区专属标签页
          const mappingsResult = await StorageManager.getTabIdMappings();

          if (mappingsResult.success) {
            const mappings = mappingsResult.data!;
            const relatedMappings = mappings.filter(mapping =>
              mapping.workspaceId === workspaceId &&
              mapping.websiteId === websiteId &&
              mapping.isWorkspaceCore
            );

            console.log(`🔍 网站 ${websiteId} 关联的工作区专属标签页: ${relatedMappings.length} 个`);

            // 降级每个相关的工作区专属标签页
            for (const mapping of relatedMappings) {
              const demoteResult = await WorkonaTabManager.demoteToSessionTab(mapping.workonaId);
              if (demoteResult.success) {
                console.log(`⬇️ 批量操作：成功降级标签页 ${mapping.workonaId}`);
              } else {
                console.warn(`⚠️ 批量操作：降级标签页失败 ${mapping.workonaId}`, demoteResult.error);
              }
            }
          }

          // 删除网站
          await removeWebsite(workspaceId, websiteId);
        } catch (error) {
          console.warn(`处理网站 ${websiteId} 的批量删除时出错:`, error);
          // 继续处理其他网站，不中断批量操作
        }
      }

      console.log(`✅ 批量删除完成: ${websiteIds.length} 个网站`);
    } catch (err) {
      errorHandler.handleWebsiteError(err, '批量删除网站');
    }
  };



  // 优化：使用平滑过渡动画替代突兀的条件渲染
  return (
    <ErrorBoundary>
      <div className="h-full relative bg-slate-900 text-white">
        {/* 骨架屏加载状态覆盖层 - 平滑过渡 */}
        <div
          className={`
            absolute inset-0 bg-slate-900 z-20
            transition-all duration-500 ease-in-out
            ${loading ? 'opacity-100 visible' : 'opacity-0 invisible'}
          `}
        >
          <AppSkeleton />
        </div>

        {/* 主界面内容 - 平滑过渡 */}
        <div
          className={`
            h-full flex flex-col
            transition-all duration-500 ease-in-out
            ${loading ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}
          `}
        >
          {/* 头部 */}
          <Header
            activeWorkspace={activeWorkspace}
            onSettingsClick={() => setShowSettings(true)}
            onCreateWorkspaceClick={() => setShowCreateModal(true)}
          />

          {/* 主内容区域 - 充满布局 */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* 工作区列表 - 消除左右间距 */}
            <div className="flex-1 relative">
              {workspaces.length === 0 ? (
                <div className="h-full flex items-center justify-center px-4 py-8">
                  <div className="text-center animate-fade-in animate-delay-200">
                    <div className="text-5xl mb-3">🚀</div>
                    <h3 className="text-base font-semibold text-slate-200 mb-2">
                      欢迎使用 WorkSpace Pro
                    </h3>
                    <p className="text-slate-400 text-sm mb-4">
                      创建您的第一个工作区来开始管理标签页
                    </p>
                    <button
                      onClick={() => setShowCreateModal(true)}
                      className="btn-primary text-sm mx-auto"
                    >
                      <Plus className="w-4 h-4" />
                      创建工作区
                    </button>
                  </div>
                </div>
              ) : (
                <WorkspaceList
                  workspaces={workspaces}
                  activeWorkspaceId={activeWorkspace?.id || null}
                  workspaceSetupStatus={workspaceSetupStatus}
                  onSwitchWorkspace={handleSwitchWorkspace}
                  onUpdateWorkspace={updateWorkspace}
                  onDeleteWorkspace={deleteWorkspace}
                  onAddCurrentTab={handleAddCurrentTab}
                  onAddWebsiteUrl={handleAddWebsiteUrl}
                  onRemoveWebsite={removeWebsite}
                  onUpdateWebsite={handleUpdateWebsite}
                  onReorderWorkspaces={reorderWorkspaces}
                  onReorderWebsites={reorderWebsites}
                  onTogglePin={handleTogglePin}
                  onBatchPin={handleBatchPin}
                  onBatchDelete={handleBatchDelete}
                />
              )}
            </div>
          </div>
        </div>

        {/* 创建工作区模态框 */}
        {showCreateModal && (
          <CreateWorkspaceModal
            onClose={() => setShowCreateModal(false)}
            onCreate={handleCreateWorkspace}
          />
        )}

        {/* 设置面板 */}
        {showSettings && (
          <SettingsPanel
            onClose={() => setShowSettings(false)}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};

/**
 * 主应用组件
 */
const App: React.FC = () => {
  return (
    <ToastProvider>
      <AppContent />
    </ToastProvider>
  );
};

export default App;
