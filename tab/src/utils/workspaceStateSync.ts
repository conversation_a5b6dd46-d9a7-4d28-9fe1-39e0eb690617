/**
 * 工作区状态同步工具
 * 用于确保工作区切换后所有组件状态保持同步
 */

export class WorkspaceStateSync {
  /**
   * 发送工作区状态更新事件
   */
  static sendWorkspaceStateUpdate(workspaceId: string, eventType: 'switch' | 'userTabsVisibility'): void {
    try {
      // 发送自定义事件
      if (typeof window !== 'undefined') {
        const eventName = eventType === 'switch' ? 'workspaceSwitchComplete' : 'userTabsVisibilityChanged';
        const event = new CustomEvent(eventName, {
          detail: { workspaceId }
        });
        window.dispatchEvent(event);
      }

      // 发送Chrome扩展消息
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const messageType = eventType === 'switch' ? 'WORKSPACE_SWITCH_COMPLETE' : 'USER_TABS_VISIBILITY_CHANGED';
        chrome.runtime.sendMessage({
          type: messageType,
          workspaceId: workspaceId
        }).catch(error => {
          console.log(`发送${eventType}事件消息失败:`, error);
        });
      }
    } catch (error) {
      console.error(`发送工作区状态更新事件失败:`, error);
    }
  }

  /**
   * 添加工作区状态监听器
   */
  static addStateListener(
    callback: (workspaceId: string, eventType: 'switch' | 'userTabsVisibility') => void
  ): () => void {
    const handleWorkspaceSwitchComplete = (event: CustomEvent) => {
      callback(event.detail.workspaceId, 'switch');
    };

    const handleUserTabsVisibilityChanged = (event: CustomEvent) => {
      callback(event.detail.workspaceId, 'userTabsVisibility');
    };

    const handleChromeMessage = (message: any) => {
      if (message.type === 'WORKSPACE_SWITCH_COMPLETE') {
        callback(message.workspaceId, 'switch');
      } else if (message.type === 'USER_TABS_VISIBILITY_CHANGED') {
        callback(message.workspaceId, 'userTabsVisibility');
      }
    };

    // 添加事件监听器
    if (typeof window !== 'undefined') {
      window.addEventListener('workspaceSwitchComplete', handleWorkspaceSwitchComplete as EventListener);
      window.addEventListener('userTabsVisibilityChanged', handleUserTabsVisibilityChanged as EventListener);
    }

    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener(handleChromeMessage);
    }

    // 返回清理函数
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('workspaceSwitchComplete', handleWorkspaceSwitchComplete as EventListener);
        window.removeEventListener('userTabsVisibilityChanged', handleUserTabsVisibilityChanged as EventListener);
      }
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.removeListener(handleChromeMessage);
      }
    };
  }
}
