import {
  WorkSpace,
  Website,
  Settings,
  StorageData,
  OperationResult,
  TabIdMapping,
  LocalOpenWorkspaces,
  TabGroups,
  WorkspaceSession
} from '@/types/workspace';
import {
  STORAGE_KEYS,
  WORKONA_STORAGE_KEYS,
  DEFAULT_SETTINGS,
  ERROR_CODES
} from './constants';
import { ImportDataProcessor } from './importDataProcessor';

/**
 * 存储管理类
 */
export class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData(): Promise<OperationResult<StorageData>> {
    try {
      // 获取基础数据和 Workona 扩展数据
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS,
        // Workona 风格数据
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKO<PERSON>_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID,
        WORKONA_STORAGE_KEYS.DATA_VERSION,
      ]);

      const data: StorageData = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [],

        // Workona 风格扩展数据（可选）
        tabIdMappings: result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [],
        localOpenWorkspaces: result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {},
        tabGroups: result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {},
        workspaceSessions: result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {},
        globalWorkspaceWindowId: result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || undefined,
        dataVersion: result[WORKONA_STORAGE_KEYS.DATA_VERSION] || '1.0.0',
      };

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get storage data',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有工作区
   */
  static async getWorkspaces(): Promise<OperationResult<WorkSpace[]>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      
      // 按order字段排序
      workspaces.sort((a: WorkSpace, b: WorkSpace) => a.order - b.order);
      
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces: WorkSpace[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 获取单个工作区
   */
  static async getWorkspace(id: string): Promise<OperationResult<WorkSpace>> {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }

    const workspace = result.data!.find(w => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`,
        },
      };
    }

    return { success: true, data: workspace };
  }

  /**
   * 获取设置
   */
  static async getSettings(): Promise<OperationResult<Settings>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get settings',
          details: error,
        },
      };
    }
  }

  /**
   * 保存设置
   */
  static async saveSettings(settings: Partial<Settings>): Promise<OperationResult<void>> {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }

      const updatedSettings = { ...currentResult.data!, ...settings };
      
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings,
      });
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save settings',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId(): Promise<OperationResult<string | null>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get active workspace ID',
          details: error,
        },
      };
    }
  }

  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id: string | null): Promise<OperationResult<void>> {
    try {
      // 🛡️ 工作区持久性保护：记录所有工作区状态变化
      const currentIdResult = await this.getActiveWorkspaceId();
      const currentId = currentIdResult.success ? currentIdResult.data : null;

      if (currentId !== id) {
        // 获取调用栈信息用于调试
        const stack = new Error().stack;
        const caller = stack?.split('\n')[2]?.trim() || 'unknown';

        if (id === null) {
          console.warn(`🚨 工作区状态被清除: ${currentId} -> null`);
          console.warn(`🔍 调用来源: ${caller}`);
          console.warn(`📋 调用栈:`, stack);
        } else {
          console.log(`🔄 工作区状态变化: ${currentId} -> ${id}`);
          console.log(`🔍 调用来源: ${caller}`);
        }
      }

      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id,
      });

      // 更新最近使用的工作区列表
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set active workspace ID',
          details: error,
        },
      };
    }
  }

  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds: string[] = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      
      // 移除已存在的ID
      lastActiveIds = lastActiveIds.filter(id => id !== workspaceId);
      
      // 添加到开头
      lastActiveIds.unshift(workspaceId);
      
      // 限制数量
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data!.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds,
      });
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update last active workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 清除所有数据（增强版：确保清除所有存储键）
   */
  static async clearAll(): Promise<OperationResult<void>> {
    try {
      console.log('🗑️ 开始清除所有数据...');

      // 获取所有存储键
      const allKeys = [
        // 基础存储键
        ...Object.values(STORAGE_KEYS),
        // Workona 存储键
        ...Object.values(WORKONA_STORAGE_KEYS),
      ];

      // 获取所有实际存储的键
      const allStoredData = await chrome.storage.local.get(null);
      const storedKeys = Object.keys(allStoredData);

      // 找出所有匹配模式的键（包括动态生成的键）
      const keysToRemove = storedKeys.filter(key => {
        return key.startsWith('workspacePinnedTabIds_') ||
               key.includes('-hidden-tabs') ||
               allKeys.includes(key);
      });

      console.log(`🔍 发现 ${keysToRemove.length} 个存储键需要清除`);

      // 清除所有数据
      await chrome.storage.local.clear();

      console.log('✅ 所有数据已清除');
      return { success: true };
    } catch (error) {
      console.error('❌ 清除数据失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear storage',
          details: error,
        },
      };
    }
  }

  /**
   * 监听存储变化
   */
  static onChanged(callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local') {
        callback(changes);
      }
    });
  }

  /**
   * 导出数据（增强版：包含完整的工作区数据结构）
   */
  static async exportData(): Promise<OperationResult<string>> {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }

      const data = dataResult.data!;

      // 构建完整的导出数据结构
      const exportData = {
        version: '2.0.0', // 升级版本号以支持新的数据结构
        exportedAt: Date.now(),

        // 基础数据
        workspaces: data.workspaces,
        settings: data.settings,
        activeWorkspaceId: data.activeWorkspaceId,
        lastActiveWorkspaceIds: data.lastActiveWorkspaceIds,

        // 工作区增强数据
        workspaceSessions: data.workspaceSessions || {},
        tabIdMappings: data.tabIdMappings || [],
        localOpenWorkspaces: data.localOpenWorkspaces || {},
        tabGroups: data.tabGroups || {},
        globalWorkspaceWindowId: data.globalWorkspaceWindowId,
        dataVersion: data.dataVersion || '1.0.0',

        // 导出元数据
        exportMetadata: {
          totalWorkspaces: data.workspaces.length,
          totalWebsites: data.workspaces.reduce((sum, ws) => sum + ws.websites.length, 0),
          totalSessions: Object.keys(data.workspaceSessions || {}).length,
          totalTabMappings: (data.tabIdMappings || []).length,
          hasActiveWorkspace: !!data.activeWorkspaceId,
          exportSource: 'WorkSpace Pro Chrome Extension'
        }
      };

      console.log(`📦 导出完整数据: ${exportData.exportMetadata.totalWorkspaces} 个工作区, ${exportData.exportMetadata.totalWebsites} 个网站, ${exportData.exportMetadata.totalSessions} 个会话`);

      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to export data',
          details: error,
        },
      };
    }
  }

  /**
   * 导入数据（增强版：支持完整数据结构和版本兼容）
   */
  static async importData(jsonData: string): Promise<OperationResult<void>> {
    try {
      let importData = JSON.parse(jsonData);

      // 验证导入数据
      const validation = ImportDataProcessor.validateImportedData(importData);
      if (!validation.isValid) {
        throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 清理导入数据
      importData = ImportDataProcessor.cleanImportData(importData);

      // 检查数据版本和格式
      const version = importData.version || '1.0.0';
      console.log(`📥 导入数据版本: ${version}`);

      // 验证数据完整性
      if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
        throw new Error('Invalid data format: workspaces array is required');
      }

      // 增量导入：获取现有工作区数据
      const existingWorkspacesResult = await this.getWorkspaces();
      const existingWorkspaces = existingWorkspacesResult.success ? existingWorkspacesResult.data! : [];

      console.log(`📋 开始增量导入 ${importData.workspaces.length} 个工作区（现有 ${existingWorkspaces.length} 个）`);

      // 执行增量导入逻辑
      const importResult = await this.performIncrementalImport(existingWorkspaces, importData.workspaces);

      // 保存合并后的工作区数据
      await this.saveWorkspaces(importResult.mergedWorkspaces);

      console.log(`✅ 增量导入完成: 新增 ${importResult.addedWorkspaces} 个工作区, 新增 ${importResult.addedWebsites} 个网站, 跳过 ${importResult.skippedWorkspaces} 个重复工作区`);

      if (importData.settings) {
        console.log('⚙️ 导入设置配置');
        await this.saveSettings(importData.settings);
      }

      // 导入增强数据（如果存在）
      if (version === '2.0.0' || importData.workspaceSessions) {
        console.log('🔄 导入工作区会话数据');

        if (importData.workspaceSessions) {
          await this.saveWorkspaceSessions(importData.workspaceSessions);
          console.log(`📊 导入 ${Object.keys(importData.workspaceSessions).length} 个工作区会话`);
        }

        if (importData.tabIdMappings) {
          await this.saveTabIdMappings(importData.tabIdMappings);
          console.log(`🔗 导入 ${importData.tabIdMappings.length} 个标签页ID映射`);
        }

        if (importData.localOpenWorkspaces) {
          await this.saveLocalOpenWorkspaces(importData.localOpenWorkspaces);
          console.log(`💻 导入本地打开工作区数据`);
        }

        if (importData.tabGroups) {
          await this.saveTabGroups(importData.tabGroups);
          console.log(`📁 导入标签组数据`);
        }

        if (importData.globalWorkspaceWindowId) {
          await chrome.storage.local.set({
            [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: importData.globalWorkspaceWindowId
          });
          console.log(`🪟 导入全局工作区窗口ID`);
        }

        if (importData.dataVersion) {
          await this.saveDataVersion(importData.dataVersion);
        }
      } else {
        // 对于旧版本数据，初始化空的增强数据结构
        console.log('🔄 初始化增强数据结构（兼容旧版本）');
        await this.saveWorkspaceSessions({});
        await this.saveTabIdMappings([]);
        await this.saveLocalOpenWorkspaces({});
        await this.saveTabGroups({});
        await this.saveDataVersion('2.0.0');
      }

      // 执行导入后的系统映射自动补全
      console.log('🔄 开始系统映射自动补全...');
      const processingResult = await ImportDataProcessor.processImportedData(importData);
      if (!processingResult.success) {
        console.warn('⚠️ 系统映射补全失败，但导入数据已保存:', processingResult.error);
      }

      // 显示导入摘要
      console.log(`✅ 增量导入完成: 新增 ${importResult.addedWorkspaces} 个工作区, 新增 ${importResult.addedWebsites} 个网站`);
      if (importResult.skippedWorkspaces > 0) {
        console.log(`ℹ️ 跳过 ${importResult.skippedWorkspaces} 个重复工作区`);
      }

      return { success: true };
    } catch (error) {
      console.error('❌ 导入数据失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to import data',
          details: error,
        },
      };
    }
  }

  // ===== Workona 风格存储方法 =====

  /**
   * 保存标签页ID映射表
   */
  static async saveTabIdMappings(mappings: TabIdMapping[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]: mappings,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save tab ID mappings',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签页ID映射表
   */
  static async getTabIdMappings(): Promise<OperationResult<TabIdMapping[]>> {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]);
      const mappings = result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [];
      return { success: true, data: mappings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get tab ID mappings',
          details: error,
        },
      };
    }
  }

  /**
   * 保存本地打开工作区
   */
  static async saveLocalOpenWorkspaces(workspaces: LocalOpenWorkspaces): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]: workspaces,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save local open workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 获取本地打开工作区
   */
  static async getLocalOpenWorkspaces(): Promise<OperationResult<LocalOpenWorkspaces>> {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]);
      const workspaces = result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {};
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get local open workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 保存标签组信息
   */
  static async saveTabGroups(tabGroups: TabGroups): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_GROUPS]: tabGroups,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save tab groups',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签组信息
   */
  static async getTabGroups(): Promise<OperationResult<TabGroups>> {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.TAB_GROUPS]);
      const tabGroups = result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {};
      return { success: true, data: tabGroups };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get tab groups',
          details: error,
        },
      };
    }
  }

  /**
   * 保存工作区会话
   */
  static async saveWorkspaceSessions(sessions: Record<string, WorkspaceSession>): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]: sessions,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save workspace sessions',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区会话
   */
  static async getWorkspaceSessions(): Promise<OperationResult<Record<string, WorkspaceSession>>> {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]);
      const sessions = result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {};
      return { success: true, data: sessions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace sessions',
          details: error,
        },
      };
    }
  }

  /**
   * 保存全局工作区窗口ID
   */
  static async saveGlobalWorkspaceWindowId(windowId: number): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: windowId,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save global workspace window ID',
          details: error,
        },
      };
    }
  }

  /**
   * 获取全局工作区窗口ID
   */
  static async getGlobalWorkspaceWindowId(): Promise<OperationResult<number | null>> {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]);
      const windowId = result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || null;
      return { success: true, data: windowId };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get global workspace window ID',
          details: error,
        },
      };
    }
  }

  /**
   * 保存数据版本
   */
  static async saveDataVersion(version: string): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.DATA_VERSION]: version,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save data version',
          details: error,
        },
      };
    }
  }

  /**
   * 获取数据版本
   */
  static async getDataVersion(): Promise<OperationResult<string>> {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.DATA_VERSION]);
      const version = result[WORKONA_STORAGE_KEYS.DATA_VERSION] || '1.0.0';
      return { success: true, data: version };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get data version',
          details: error,
        },
      };
    }
  }

  /**
   * 清除全局工作区窗口ID
   */
  static async clearGlobalWorkspaceWindowId(): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.remove([WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear global workspace window ID',
          details: error,
        },
      };
    }
  }

  /**
   * 渐进式数据迁移检查
   * 检测现有数据格式，逐步迁移到 Workona 格式
   */
  static async migrateToWorkonaFormat(): Promise<OperationResult<boolean>> {
    try {
      console.log('🔄 开始检查 Workona 数据迁移需求...');

      // 检查当前数据版本
      const versionResult = await this.getDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }

      const currentVersion = versionResult.data!;
      const targetVersion = '1.0.0';

      // 如果版本已经是最新，无需迁移
      if (currentVersion === targetVersion) {
        console.log('✅ 数据版本已是最新，无需迁移');
        return { success: true, data: false };
      }

      console.log(`📦 检测到数据版本 ${currentVersion}，开始迁移到 ${targetVersion}...`);

      // 获取现有数据
      const allDataResult = await this.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }

      const data = allDataResult.data!;

      // 如果 Workona 数据不存在，初始化空数据
      if (!data.tabIdMappings) {
        await this.saveTabIdMappings([]);
      }

      if (!data.localOpenWorkspaces) {
        await this.saveLocalOpenWorkspaces({});
      }

      if (!data.tabGroups) {
        await this.saveTabGroups({});
      }

      if (!data.workspaceSessions) {
        await this.saveWorkspaceSessions({});
      }

      // 更新数据版本
      await this.saveDataVersion(targetVersion);

      console.log('✅ Workona 数据迁移完成');
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ Workona 数据迁移失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to migrate to Workona format',
          details: error,
        },
      };
    }
  }

  /**
   * 执行增量导入逻辑（极简重构版 - 绝对安全）
   *
   * 核心原则：
   * 1. 永远不删除现有数据
   * 2. 只进行添加操作
   * 3. 使用最简单的逻辑
   * 4. 每个操作都是原子的和可验证的
   */
  private static async performIncrementalImport(
    existingWorkspaces: WorkSpace[],
    importWorkspaces: WorkSpace[]
  ): Promise<{
    mergedWorkspaces: WorkSpace[];
    addedWorkspaces: number;
    addedWebsites: number;
    skippedWorkspaces: number;
  }> {
    console.log(`🚀 开始极简增量导入`);
    console.log(`📊 现有工作区: ${existingWorkspaces.length} 个, 导入工作区: ${importWorkspaces.length} 个`);

    // 第一步：完整深拷贝所有现有工作区作为结果
    const result: WorkSpace[] = JSON.parse(JSON.stringify(existingWorkspaces));
    console.log(`✅ 深拷贝现有工作区完成: ${result.length} 个`);

    // 统计变量
    let addedWorkspaces = 0;
    let addedWebsites = 0;
    let skippedWorkspaces = 0;

    // 第二步：逐个处理导入工作区
    for (const importWorkspace of importWorkspaces) {
      console.log(`\n🔄 处理导入工作区: "${importWorkspace.name}"`);

      // 查找是否有同名的现有工作区
      const existingWorkspace = result.find(ws => ws.name.toLowerCase() === importWorkspace.name.toLowerCase());

      if (existingWorkspace) {
        // 有同名工作区：合并网站
        console.log(`📝 发现同名工作区，开始合并网站...`);
        const addedCount = this.safelyMergeWebsites(existingWorkspace, importWorkspace);
        addedWebsites += addedCount;
        skippedWorkspaces++;
        console.log(`✅ 合并完成: 向 "${importWorkspace.name}" 添加了 ${addedCount} 个新网站`);
      } else {
        // 没有同名工作区：创建新工作区
        console.log(`🆕 创建新工作区...`);
        const newWorkspace = this.safelyCreateNewWorkspace(importWorkspace, result);
        result.push(newWorkspace);
        addedWorkspaces++;
        const websiteCount = newWorkspace.websites ? newWorkspace.websites.length : 0;
        addedWebsites += websiteCount;
        console.log(`✅ 创建完成: "${newWorkspace.name}" 包含 ${websiteCount} 个网站`);
      }
    }

    console.log(`\n🎉 增量导入完成!`);
    console.log(`📊 最终结果: 工作区 ${result.length} 个, 新增工作区 ${addedWorkspaces} 个, 合并工作区 ${skippedWorkspaces} 个, 新增网站 ${addedWebsites} 个`);

    return {
      mergedWorkspaces: result,
      addedWorkspaces,
      addedWebsites,
      skippedWorkspaces
    };
  }

  /**
   * 安全地合并网站到现有工作区（极简版 - 绝对不会删除现有网站）
   */
  private static safelyMergeWebsites(
    existingWorkspace: WorkSpace,
    importWorkspace: WorkSpace
  ): number {
    console.log(`📊 合并前: 现有工作区 "${existingWorkspace.name}" 有 ${existingWorkspace.websites?.length || 0} 个网站`);

    // 确保现有工作区有websites数组
    if (!existingWorkspace.websites) {
      existingWorkspace.websites = [];
    }

    // 记录原始网站数量（用于验证）
    const originalCount = existingWorkspace.websites.length;
    console.log(`📋 现有网站列表:`, existingWorkspace.websites.map(w => `${w.title} (${w.url})`));

    // 构建现有URL集合（只检查当前工作区内的重复）
    const existingUrls = new Set<string>();
    existingWorkspace.websites.forEach(website => {
      existingUrls.add(website.url.toLowerCase());
    });

    let addedCount = 0;

    // 处理导入工作区的网站
    if (importWorkspace.websites && importWorkspace.websites.length > 0) {
      console.log(`📋 导入网站列表:`, importWorkspace.websites.map(w => `${w.title} (${w.url})`));

      for (const importWebsite of importWorkspace.websites) {
        const urlLower = importWebsite.url.toLowerCase();

        if (!existingUrls.has(urlLower)) {
          // 创建新网站（深拷贝 + 新ID）
          const newWebsite: Website = {
            ...JSON.parse(JSON.stringify(importWebsite)),
            id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          };

          // 添加到现有工作区（只添加，绝不删除）
          existingWorkspace.websites.push(newWebsite);
          existingUrls.add(urlLower);
          addedCount++;

          console.log(`✅ 添加新网站: ${importWebsite.title} (${importWebsite.url})`);
        } else {
          console.log(`⏭️ 跳过重复网站: ${importWebsite.title} (${importWebsite.url})`);
        }
      }
    }

    // 更新时间戳
    existingWorkspace.updatedAt = Date.now();

    // 验证结果（确保没有丢失现有网站）
    const finalCount = existingWorkspace.websites.length;
    const expectedCount = originalCount + addedCount;

    console.log(`📊 合并后: 工作区 "${existingWorkspace.name}" 有 ${finalCount} 个网站`);
    console.log(`📋 最终网站列表:`, existingWorkspace.websites.map(w => `${w.title} (${w.url})`));

    if (finalCount !== expectedCount) {
      console.error(`❌ 严重错误! 网站数量不匹配: 预期 ${expectedCount}, 实际 ${finalCount}`);
      throw new Error(`网站合并验证失败: ${existingWorkspace.name}`);
    }

    console.log(`✅ 验证通过: 保留 ${originalCount} 个现有网站, 新增 ${addedCount} 个网站`);
    return addedCount;
  }

  /**
   * 安全地创建新工作区（极简版 - 检查全局URL重复）
   */
  private static safelyCreateNewWorkspace(
    importWorkspace: WorkSpace,
    existingWorkspaces: WorkSpace[]
  ): WorkSpace {
    console.log(`🆕 创建新工作区: "${importWorkspace.name}"`);

    // 构建全局URL集合（来自所有现有工作区）
    const globalUrls = new Set<string>();
    existingWorkspaces.forEach(workspace => {
      if (workspace.websites) {
        workspace.websites.forEach(website => {
          globalUrls.add(website.url.toLowerCase());
        });
      }
    });

    // 处理导入工作区的网站
    const processedWebsites: Website[] = [];

    if (importWorkspace.websites && importWorkspace.websites.length > 0) {
      console.log(`📋 导入网站列表:`, importWorkspace.websites.map(w => `${w.title} (${w.url})`));

      for (const importWebsite of importWorkspace.websites) {
        const urlLower = importWebsite.url.toLowerCase();

        if (!globalUrls.has(urlLower)) {
          // 创建新网站（深拷贝 + 新ID）
          const newWebsite: Website = {
            ...JSON.parse(JSON.stringify(importWebsite)),
            id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          };

          processedWebsites.push(newWebsite);
          globalUrls.add(urlLower); // 更新全局URL集合

          console.log(`✅ 添加网站: ${importWebsite.title} (${importWebsite.url})`);
        } else {
          console.log(`⏭️ 跳过全局重复网站: ${importWebsite.title} (${importWebsite.url})`);
        }
      }
    }

    // 创建新工作区（深拷贝 + 新ID）
    const newWorkspace: WorkSpace = {
      ...JSON.parse(JSON.stringify(importWorkspace)),
      id: `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      websites: processedWebsites,
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    console.log(`✅ 新工作区创建完成: "${newWorkspace.name}" 包含 ${processedWebsites.length} 个网站`);
    console.log(`📋 网站列表:`, processedWebsites.map(w => `${w.title} (${w.url})`));

    return newWorkspace;
  }

}
