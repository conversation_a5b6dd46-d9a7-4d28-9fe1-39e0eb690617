import React, { useEffect, useRef } from 'react';
import { LucideIcon } from 'lucide-react';

interface MenuItem {
  id: string;
  label: string;
  icon: LucideIcon;
  className?: string;
}

interface DropdownMenuProps {
  items: MenuItem[];
  onItemClick: (itemId: string) => void;
  onClose: () => void;
}

/**
 * 下拉菜单组件
 */
const DropdownMenu: React.FC<DropdownMenuProps> = ({
  items,
  onItemClick,
  onClose,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  /**
   * 处理点击外部关闭
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  /**
   * 处理ESC键关闭
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  /**
   * 处理菜单项点击
   */
  const handleItemClick = (itemId: string) => {
    onItemClick(itemId);
  };

  return (
    <div
      ref={menuRef}
      className="dropdown-menu animate-fade-in"
    >
      {items.map((item) => {
        const Icon = item.icon;
        return (
          <button
            key={item.id}
            onClick={() => handleItemClick(item.id)}
            className={`dropdown-item ${item.className || ''}`}
          >
            <Icon className="w-4 h-4" />
            <span>{item.label}</span>
          </button>
        );
      })}
    </div>
  );
};

export default DropdownMenu;
