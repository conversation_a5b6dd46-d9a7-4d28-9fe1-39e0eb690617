import React from 'react';

/**
 * 骨架屏加载组件 - 模拟真实界面结构
 */
interface SkeletonLoaderProps {
  type?: 'workspace' | 'website' | 'header';
  count?: number;
  className?: string;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ 
  type = 'workspace', 
  count = 1,
  className = ''
}) => {
  // 工作区骨架屏
  const renderWorkspaceSkeleton = () => (
    <div className="bg-slate-800 rounded-lg p-3 animate-pulse">
      {/* 工作区头部骨架 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          {/* 工作区图标 */}
          <div className="w-3 h-3 bg-slate-700 rounded"></div>
          {/* 工作区名称 */}
          <div className="h-4 bg-slate-700 rounded w-24"></div>
          {/* 状态指示器 */}
          <div className="w-2 h-2 bg-slate-700 rounded-full"></div>
        </div>
        {/* 操作按钮组 */}
        <div className="flex gap-2">
          <div className="w-6 h-6 bg-slate-700 rounded"></div>
          <div className="w-6 h-6 bg-slate-700 rounded"></div>
          <div className="w-6 h-6 bg-slate-700 rounded"></div>
        </div>
      </div>
      
      {/* 网站列表骨架 */}
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center gap-2 px-2 py-1.5">
            {/* 网站图标 */}
            <div className="w-4 h-4 bg-slate-700 rounded"></div>
            {/* 网站标题 */}
            <div className="flex-1 h-3 bg-slate-700 rounded"></div>
            {/* 操作按钮 */}
            <div className="w-4 h-4 bg-slate-700 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );

  // 网站项目骨架屏
  const renderWebsiteSkeleton = () => (
    <div className="flex items-center gap-2 px-2 py-1.5 animate-pulse">
      <div className="w-4 h-4 bg-slate-700 rounded"></div>
      <div className="flex-1 h-3 bg-slate-700 rounded"></div>
      <div className="w-4 h-4 bg-slate-700 rounded"></div>
    </div>
  );

  // 头部骨架屏
  const renderHeaderSkeleton = () => (
    <div className="flex items-center justify-between p-4 border-b border-slate-700 animate-pulse">
      <div className="flex items-center gap-3">
        {/* Logo */}
        <div className="w-8 h-8 bg-slate-700 rounded-lg"></div>
        {/* 标题 */}
        <div className="h-5 bg-slate-700 rounded w-32"></div>
      </div>
      <div className="flex gap-2">
        {/* 设置按钮 */}
        <div className="w-8 h-8 bg-slate-700 rounded"></div>
        {/* 添加按钮 */}
        <div className="w-8 h-8 bg-slate-700 rounded"></div>
      </div>
    </div>
  );

  // 根据类型渲染对应的骨架屏
  const renderSkeleton = () => {
    switch (type) {
      case 'workspace':
        return renderWorkspaceSkeleton();
      case 'website':
        return renderWebsiteSkeleton();
      case 'header':
        return renderHeaderSkeleton();
      default:
        return renderWorkspaceSkeleton();
    }
  };

  return (
    <div className={className}>
      {[...Array(count)].map((_, index) => (
        <div key={index} className={index > 0 ? 'mt-3' : ''}>
          {renderSkeleton()}
        </div>
      ))}
    </div>
  );
};

/**
 * 完整的应用骨架屏 - 模拟整个应用界面
 */
export const AppSkeleton: React.FC = () => {
  return (
    <div className="h-full bg-slate-900 text-white relative">
      {/* 头部骨架 */}
      <SkeletonLoader type="header" />
      
      {/* 内容区域骨架 */}
      <div className="p-4 space-y-4">
        {/* 渲染多个工作区骨架 */}
        <SkeletonLoader type="workspace" count={4} />
      </div>
      
      {/* 流光效果 - 增强加载感知 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-800/10 to-transparent animate-pulse pointer-events-none"></div>
      
      {/* 底部加载提示 */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center">
        <div className="bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2 border border-slate-700/50">
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-slate-300 text-xs">正在加载工作区数据...</p>
        </div>
      </div>
    </div>
  );
};

/**
 * 工作区列表专用骨架屏
 */
export const WorkspaceListSkeleton: React.FC = () => {
  return (
    <div className="p-4 space-y-3">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="bg-slate-800 rounded-lg p-3 animate-pulse">
          {/* 工作区头部 */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-slate-700 rounded"></div>
              <div className="h-4 bg-slate-700 rounded w-20"></div>
            </div>
            <div className="flex gap-1">
              <div className="w-5 h-5 bg-slate-700 rounded"></div>
              <div className="w-5 h-5 bg-slate-700 rounded"></div>
            </div>
          </div>
          {/* 网站列表 */}
          <div className="space-y-1">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="w-3 h-3 bg-slate-700 rounded"></div>
                <div className="flex-1 h-3 bg-slate-700 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SkeletonLoader;
