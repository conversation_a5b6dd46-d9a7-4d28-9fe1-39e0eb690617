import React, { useState } from 'react';
import { X, Sparkles } from 'lucide-react';
import { WORKSPACE_COLORS, WORKSPACE_ICONS } from '@/utils/constants';
import { useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

interface CreateWorkspaceModalProps {
  onClose: () => void;
  onCreate: (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
    addCurrentTabs?: boolean;
  }) => void;
}

/**
 * 创建工作区模态框
 */
const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  onClose,
  onCreate,
}) => {
  const [name, setName] = useState('');
  const [selectedIcon, setSelectedIcon] = useState<string>(WORKSPACE_ICONS[0]);
  const [selectedColor, setSelectedColor] = useState<string>(WORKSPACE_COLORS[0]);
  const [addCurrentTabs, setAddCurrentTabs] = useState(false);

  // Toast 错误处理
  const { showError } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      errorHandler.handle('工作区名称不能为空');
      return;
    }

    try {
      onCreate(name.trim(), {
        icon: selectedIcon,
        color: selectedColor,
        addCurrentTabs,
      });
    } catch (error) {
      errorHandler.handleWorkspaceError(error, '创建');
    }
  };



  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            创建新工作区
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>



        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          {/* 工作区名称 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              工作区名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入工作区名称..."
              className="input-field w-full"
              autoFocus
            />
          </div>

          {/* 图标选择 - 优化360px宽度 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择图标
            </label>
            <div className="grid grid-cols-6 gap-1.5 max-h-20 overflow-y-auto">
              {WORKSPACE_ICONS.map((icon) => (
                <button
                  key={icon}
                  type="button"
                  onClick={() => setSelectedIcon(icon)}
                  className={`p-1.5 rounded text-base transition-colors duration-200 ${
                    selectedIcon === icon
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
          </div>

          {/* 颜色选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择颜色
            </label>
            <div className="flex gap-2 flex-wrap">
              {WORKSPACE_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-8 rounded-lg transition-all duration-200 ${
                    selectedColor === color
                      ? 'ring-2 ring-white ring-offset-2 ring-offset-slate-800'
                      : 'hover:scale-110'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* 添加当前标签页选项 */}
          <div className="mb-4">
            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={addCurrentTabs}
                onChange={(e) => setAddCurrentTabs(e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2"
              />
              <span className="text-sm text-slate-200">
                将当前标签页全部加入到该工作区
              </span>
            </label>
            <p className="text-xs text-slate-400 mt-1 ml-7">
              选中后，当前窗口中的所有标签页都会被添加到新工作区中
            </p>
          </div>

          {/* 预览 */}
          <div className="mb-4 p-3 bg-slate-700 rounded-lg">
            <div className="text-sm text-slate-200 mb-2">预览</div>
            <div className="flex items-center gap-3">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
                style={{ backgroundColor: selectedColor + '20', color: selectedColor }}
              >
                {selectedIcon}
              </div>
              <span className="text-white font-medium">
                {name || '工作区名称'}
              </span>
            </div>
          </div>



          {/* 按钮 */}
          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!name.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Sparkles className="w-4 h-4" />
              创建工作区
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateWorkspaceModal;
