/**
 * 工作区状态重置功能测试脚本
 * 在Chrome扩展的Service Worker控制台中运行此脚本来验证功能
 */

console.log('🧪 开始工作区状态重置功能测试...');

/**
 * 检查当前工作区状态
 */
async function checkWorkspaceState() {
  console.log('\n📊 检查当前工作区状态:');
  
  try {
    // 检查活跃工作区ID
    const activeResult = await chrome.storage.local.get(['activeWorkspaceId']);
    console.log('🎯 活跃工作区ID:', activeResult.activeWorkspaceId || '无');
    
    // 检查所有工作区
    const workspacesResult = await chrome.storage.local.get(['workspaces']);
    const workspaces = workspacesResult.workspaces || [];
    
    console.log('📋 工作区列表:');
    workspaces.forEach((workspace, index) => {
      const status = workspace.isActive ? '🟢 激活' : '⚪ 未激活';
      console.log(`  ${index + 1}. ${workspace.name} - ${status}`);
    });
    
    // 检查会话状态
    const sessionResult = await chrome.storage.local.get(['workspaceSessions']);
    const sessions = sessionResult.workspaceSessions || {};
    const sessionCount = Object.keys(sessions).length;
    console.log('🔄 活跃会话数量:', sessionCount);
    
    return {
      activeWorkspaceId: activeResult.activeWorkspaceId,
      workspaces: workspaces,
      activeWorkspaceCount: workspaces.filter(w => w.isActive).length,
      sessionCount: sessionCount
    };
  } catch (error) {
    console.error('❌ 检查工作区状态失败:', error);
    return null;
  }
}

/**
 * 创建测试工作区
 */
async function createTestWorkspaces() {
  console.log('\n🏗️ 创建测试工作区...');
  
  try {
    const testWorkspaces = [
      {
        id: 'test-workspace-1',
        name: '测试工作区1',
        icon: '🚀',
        color: '#3b82f6',
        websites: [
          { id: 'site1', url: 'https://example.com', title: '示例网站1' }
        ],
        isActive: false,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        id: 'test-workspace-2',
        name: '测试工作区2',
        icon: '💼',
        color: '#10b981',
        websites: [
          { id: 'site2', url: 'https://google.com', title: 'Google' }
        ],
        isActive: false,
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
    ];
    
    await chrome.storage.local.set({ workspaces: testWorkspaces });
    console.log('✅ 测试工作区创建完成');
    
    return testWorkspaces;
  } catch (error) {
    console.error('❌ 创建测试工作区失败:', error);
    return [];
  }
}

/**
 * 激活测试工作区
 */
async function activateTestWorkspace(workspaceId) {
  console.log(`\n🎯 激活测试工作区: ${workspaceId}`);
  
  try {
    // 设置活跃工作区ID
    await chrome.storage.local.set({ activeWorkspaceId: workspaceId });
    
    // 更新工作区状态
    const workspacesResult = await chrome.storage.local.get(['workspaces']);
    const workspaces = workspacesResult.workspaces || [];
    
    const updatedWorkspaces = workspaces.map(workspace => ({
      ...workspace,
      isActive: workspace.id === workspaceId
    }));
    
    await chrome.storage.local.set({ workspaces: updatedWorkspaces });
    
    console.log('✅ 工作区激活完成');
  } catch (error) {
    console.error('❌ 激活工作区失败:', error);
  }
}

/**
 * 手动重置工作区状态（模拟浏览器重启）
 */
async function manualResetWorkspaceState() {
  console.log('\n🔄 手动重置工作区状态（模拟浏览器重启）...');
  
  try {
    // 清除活跃工作区ID
    await chrome.storage.local.set({ activeWorkspaceId: null });
    
    // 重置所有工作区的激活状态
    const workspacesResult = await chrome.storage.local.get(['workspaces']);
    const workspaces = workspacesResult.workspaces || [];
    
    const resetWorkspaces = workspaces.map(workspace => ({
      ...workspace,
      isActive: false
    }));
    
    await chrome.storage.local.set({ workspaces: resetWorkspaces });
    
    console.log('✅ 手动重置完成');
  } catch (error) {
    console.error('❌ 手动重置失败:', error);
  }
}

/**
 * 运行完整测试
 */
async function runFullTest() {
  console.log('🧪 开始完整测试流程...\n');
  
  // 1. 检查初始状态
  console.log('📋 步骤1: 检查初始状态');
  const initialState = await checkWorkspaceState();
  
  // 2. 创建测试工作区（如果没有）
  if (!initialState || initialState.workspaces.length === 0) {
    console.log('📋 步骤2: 创建测试工作区');
    await createTestWorkspaces();
  }
  
  // 3. 激活一个工作区
  console.log('📋 步骤3: 激活测试工作区');
  await activateTestWorkspace('test-workspace-1');
  
  // 4. 检查激活后状态
  console.log('📋 步骤4: 检查激活后状态');
  const activatedState = await checkWorkspaceState();
  
  // 5. 模拟浏览器重启
  console.log('📋 步骤5: 模拟浏览器重启');
  await manualResetWorkspaceState();
  
  // 6. 检查重启后状态
  console.log('📋 步骤6: 检查重启后状态');
  const resetState = await checkWorkspaceState();
  
  // 7. 验证结果
  console.log('\n📊 测试结果分析:');
  
  if (resetState) {
    const success = resetState.activeWorkspaceId === null && resetState.activeWorkspaceCount === 0;
    
    if (success) {
      console.log('✅ 测试通过！工作区状态已正确重置');
      console.log('  - 活跃工作区ID已清除');
      console.log('  - 所有工作区都处于未激活状态');
    } else {
      console.log('❌ 测试失败！工作区状态未正确重置');
      console.log(`  - 活跃工作区ID: ${resetState.activeWorkspaceId}`);
      console.log(`  - 激活的工作区数量: ${resetState.activeWorkspaceCount}`);
    }
  } else {
    console.log('❌ 测试失败！无法获取重置后状态');
  }
  
  console.log('\n🏁 测试完成');
}

// 导出测试函数供手动调用
window.workspaceResetTest = {
  checkState: checkWorkspaceState,
  createTestWorkspaces: createTestWorkspaces,
  activateWorkspace: activateTestWorkspace,
  manualReset: manualResetWorkspaceState,
  runFullTest: runFullTest
};

console.log('✅ 测试脚本加载完成');
console.log('💡 使用方法:');
console.log('  - workspaceResetTest.checkState() - 检查当前状态');
console.log('  - workspaceResetTest.manualReset() - 手动重置状态');
console.log('  - workspaceResetTest.runFullTest() - 运行完整测试');
console.log('\n🔍 真实测试方法：');
console.log('  1. 激活一个工作区');
console.log('  2. 完全关闭Chrome浏览器');
console.log('  3. 重新打开Chrome浏览器');
console.log('  4. 检查扩展状态（应该没有工作区被激活）');
