{"version": 3, "file": "core.cjs.production.min.js", "sources": ["../src/components/DndMonitor/context.ts", "../src/components/DndMonitor/useDndMonitor.ts", "../src/components/Accessibility/defaults.ts", "../src/components/Accessibility/Accessibility.tsx", "../src/store/actions.ts", "../src/utilities/other/noop.ts", "../src/utilities/coordinates/constants.ts", "../src/utilities/coordinates/distanceBetweenPoints.ts", "../src/utilities/coordinates/getRelativeTransformOrigin.ts", "../src/utilities/algorithms/helpers.ts", "../src/utilities/algorithms/closestCenter.ts", "../src/utilities/algorithms/rectIntersection.ts", "../src/utilities/algorithms/pointerWithin.ts", "../src/utilities/rect/getRectDelta.ts", "../src/utilities/rect/rectAdjustment.ts", "../src/utilities/transform/parseTransform.ts", "../src/utilities/rect/getRect.ts", "../src/utilities/transform/inverseTransform.ts", "../src/utilities/scroll/getScrollableAncestors.ts", "../src/utilities/scroll/isScrollable.ts", "../src/utilities/scroll/isFixed.ts", "../src/utilities/scroll/getScrollableElement.ts", "../src/utilities/scroll/getScrollCoordinates.ts", "../src/types/direction.ts", "../src/utilities/scroll/documentScrollingElement.ts", "../src/utilities/scroll/getScrollPosition.ts", "../src/utilities/scroll/getScrollDirectionAndSpeed.ts", "../src/utilities/scroll/getScrollElementRect.ts", "../src/utilities/scroll/getScrollOffsets.ts", "../src/utilities/scroll/scrollIntoViewIfNeeded.ts", "../src/utilities/rect/Rect.ts", "../src/sensors/utilities/Listeners.ts", "../src/sensors/utilities/hasExceededDistance.ts", "../src/sensors/events.ts", "../src/sensors/keyboard/types.ts", "../src/sensors/keyboard/defaults.ts", "../src/sensors/keyboard/KeyboardSensor.ts", "../src/sensors/pointer/AbstractPointerSensor.ts", "../src/sensors/utilities/getEventListenerTarget.ts", "../src/sensors/pointer/PointerSensor.ts", "../src/sensors/mouse/MouseSensor.ts", "../src/sensors/touch/TouchSensor.ts", "../src/hooks/utilities/useAutoScroller.ts", "../src/hooks/utilities/useDroppableMeasuring.ts", "../src/hooks/utilities/useInitialValue.ts", "../src/hooks/utilities/useResizeObserver.ts", "../src/hooks/utilities/useRect.ts", "../src/hooks/utilities/useMutationObserver.ts", "../src/hooks/utilities/useScrollableAncestors.ts", "../src/hooks/utilities/useScrollOffsetsDelta.ts", "../src/hooks/utilities/useWindowRect.ts", "../src/utilities/rect/getWindowClientRect.ts", "../src/hooks/utilities/useRects.ts", "../src/utilities/nodes/getMeasurableNode.ts", "../src/components/DndContext/defaults.ts", "../src/store/constructors.ts", "../src/store/context.ts", "../src/store/reducer.ts", "../src/components/Accessibility/components/RestoreFocus.tsx", "../src/modifiers/applyModifiers.ts", "../src/components/DndContext/DndContext.tsx", "../src/components/DndMonitor/useDndMonitorProvider.tsx", "../src/components/DndContext/hooks/useMeasuringConfiguration.ts", "../src/hooks/utilities/useCachedNode.ts", "../src/hooks/utilities/useInitialRect.ts", "../src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "../src/hooks/utilities/useDragOverlayMeasuring.ts", "../src/hooks/utilities/useRectDelta.ts", "../src/hooks/utilities/useScrollOffsets.ts", "../src/utilities/rect/adjustScale.ts", "../src/hooks/utilities/useCombineActivators.ts", "../src/hooks/utilities/useSensorSetup.ts", "../src/hooks/useDraggable.ts", "../src/hooks/useDndContext.ts", "../src/hooks/useDroppable.ts", "../src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "../src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "../src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "../src/components/DragOverlay/hooks/useDropAnimation.ts", "../src/components/DragOverlay/hooks/useKey.ts", "../src/components/DragOverlay/DragOverlay.tsx", "../src/utilities/algorithms/closestCorners.ts", "../src/hooks/utilities/useSyntheticListeners.ts", "../src/sensors/useSensor.ts", "../src/sensors/useSensors.ts"], "sourcesContent": ["import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n"], "names": ["DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useState", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "event", "rect", "eventCoordinates", "getEventCoordinates", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "intersectionArea", "Number", "toFixed", "rectIntersection", "_ref", "collisionRect", "droppableRects", "droppableContainers", "droppableContainer", "get", "intersectionRatio", "push", "sort", "isPointWithinRect", "point", "getRectDelta", "rect1", "rect2", "createRectAdjustmentFn", "modifier", "adjustments", "reduce", "acc", "adjustment", "getAdjustedRect", "parseTransform", "transform", "startsWith", "transformArray", "slice", "split", "scaleX", "scaleY", "defaultOptions", "ignoreTransform", "getClientRect", "element", "options", "getBoundingClientRect", "transform<PERSON><PERSON>in", "getWindow", "getComputedStyle", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "inverseTransform", "getTransformAgnosticClientRect", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "node", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "computedStyle", "overflowRegex", "some", "test", "isScrollable", "position", "isFixed", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "innerHeight", "innerWidth", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "add", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "properties", "Rect", "constructor", "scrollOffsets", "this", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "enumerable", "Listeners", "listeners", "removeAll", "for<PERSON>ach", "_this$target", "removeEventListener", "eventName", "handler", "addEventListener", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "KeyboardCode", "preventDefault", "stopPropagation", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "EventTarget", "getEventListenerTarget", "activated", "initialCoordinates", "timeoutId", "documentListeners", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "super", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "[object Object]", "AutoScrollActivator", "TraversalOrder", "touches", "defaultScrollIntent", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useInitialValue", "computeFn", "useLazyMemo", "previousValue", "useResizeObserver", "callback", "disabled", "handleResize", "useEvent", "resizeObserver", "ResizeObserver", "disconnect", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "JSON", "stringify", "mutationObserver", "handleMutations", "MutationObserver", "useMutationObserver", "records", "record", "type", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useScrollOffsetsDelta", "dependencies", "initialScrollOffsets", "useRef", "hasScrollOffsets", "subtract", "useWindowRect", "getWindowClientRect", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "defaultSensors", "sensor", "defaultData", "defaultMeasuringConfiguration", "droppable", "strategy", "WhileDragging", "frequency", "Optimized", "dragOverlay", "DroppableContainersMap", "undefined", "toArray", "Array", "from", "values", "getEnabled", "filter", "getNodeFor", "_this$get", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "draggableNodes", "nodeRef", "setRef", "scrollableAncestorRects", "measuringConfiguration", "measureDroppableContainers", "windowRect", "measuringScheduled", "defaultInternalContext", "ariaDescribedById", "dispatch", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "containers", "reducer", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "set", "SetDroppableDisabled", "UnregisterDroppable", "delete", "RestoreFocus", "previousActivatorEvent", "usePrevious", "previousActiveId", "activeElement", "draggableNode", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "accumulator", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "autoScroll", "sensors", "collisionDetection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "Set", "useCallback", "_listener$type", "useDndMonitorProvider", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "useLatestValue", "draggableDescribedById", "enabledDroppableContainers", "config", "dragging", "queue", "setQueue", "containersRef", "Always", "BeforeDragging", "isDisabled", "disabledRef", "ids", "concat", "map", "useDroppableMeasuring", "cachedNode", "useCachedNode", "activationCoordinates", "autoScrollOptions", "enabled", "getAutoScrollerOptions", "initialActiveNodeRect", "useInitialRect", "initialRect", "initialized", "rectD<PERSON><PERSON>", "useLayoutShiftScrollCompensation", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "_sensorContext$curren", "entries", "handleNodeChange", "useNodeRef", "useDragOverlayMeasuring", "usesDragOverlay", "nodeRectDelta", "previousNode", "ancestors", "useScrollableAncestors", "elements", "firstElement", "rects", "setRects", "measureRects", "useRects", "modifiedTranslate", "overlayNodeRect", "pointerCoordinates", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "scrollableElement", "useScrollOffsets", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "adjustScale", "activeSensorRef", "instantiateSensor", "Sensor", "sensorInstance", "onDragAbort", "onDragPending", "unstable_batchedUpdates", "Initializing", "createHandler", "async", "cancelDrop", "Promise", "resolve", "getSyntheticHandler", "useCombineActivators", "activeDraggableNode", "dndKit", "defaultPrevented", "capturedBy", "teardownFns", "setup", "teardown", "_ref2", "useSensorSetup", "over<PERSON><PERSON><PERSON>", "Pointer", "canScroll", "draggingRect", "interval", "order", "TreeOrder", "scrollIntent", "previousDel<PERSON>", "previousIntent", "sign", "useScrollIntent", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "scrollDirection", "DraggableRect", "scrollContainerRef", "sortedScrollableAncestors", "reverse", "index", "useAutoScroller", "publicContext", "internalContext", "Provider", "restoreFocus", "NullContext", "defaultRole", "useDndContext", "defaultResizeObserverConfig", "timeout", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "final", "sideEffects", "opacity", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform", "activeDraggable", "measurableNode", "_ref4", "rest", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "createDefaultDropAnimation", "useDropAnimation", "centerRect", "distBetween", "corners", "rectCorners", "distances", "corner", "effectiveDistance", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "useSyntheticListeners", "dataRef", "aria-disabled", "aria-pressed", "aria-roledescription", "aria-<PERSON><PERSON>", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver"], "mappings": "+OAIO,MAAMA,EAAoBC,gBAAuC,eCCxDC,EAAcC,GAC5B,MAAMC,EAAmBC,aAAWL,GAEpCM,YAAU,KACR,IAAKF,EACH,MAAM,IAAIG,MACR,gEAMJ,OAFoBH,EAAiBD,IAGpC,CAACA,EAAUC,UChBHI,EAA4D,CACvEC,2NAOWC,EAAsC,CACjDC,mBAAYC,OAACA,KACX,kCAAmCA,EAAOC,QAE5CC,kBAAWF,OAACA,EAADG,KAASA,KAClB,OAAIA,oBACuBH,EAAOC,qCAAoCE,EAAKF,yBAGlDD,EAAOC,2CAElCG,iBAAUJ,OAACA,EAADG,KAASA,KACjB,OAAIA,oBACuBH,EAAOC,uCAAsCE,EAAKF,qBAGpDD,EAAOC,oBAElCI,oBAAaL,OAACA,KACZ,gDAAiDA,EAAOC,8BCT5CK,SAAcC,cAC5BA,EAAgBT,EADYU,UAE5BA,EAF4BC,wBAG5BA,EAH4BC,yBAI5BA,EAA2Bd,KAE3B,MAAMe,SAACA,EAADC,aAAWA,GAAgBC,oBAC3BC,EAAeC,gCACdC,EAASC,GAAcC,YAAS,GA+BvC,GA7BAxB,YAAU,KACRuB,GAAW,IACV,IAEH3B,EACE6B,UACE,MACEpB,mBAAYC,OAACA,KACXW,EAASJ,EAAcR,YAAY,CAACC,OAAAA,MAEtCoB,kBAAWpB,OAACA,EAADG,KAASA,KACdI,EAAca,YAChBT,EAASJ,EAAca,WAAW,CAACpB,OAAAA,EAAQG,KAAAA,MAG/CD,kBAAWF,OAACA,EAADG,KAASA,KAClBQ,EAASJ,EAAcL,WAAW,CAACF,OAAAA,EAAQG,KAAAA,MAE7CC,iBAAUJ,OAACA,EAADG,KAASA,KACjBQ,EAASJ,EAAcH,UAAU,CAACJ,OAAAA,EAAQG,KAAAA,MAE5CE,oBAAaL,OAACA,EAADG,KAASA,KACpBQ,EAASJ,EAAcF,aAAa,CAACL,OAAAA,EAAQG,KAAAA,QAGjD,CAACQ,EAAUJ,MAIVS,EACH,OAAO,KAGT,MAAMK,EACJC,gCACEA,gBAACC,cACCtB,GAAIQ,EACJe,MAAOd,EAAyBb,YAElCyB,gBAACG,cAAWxB,GAAIa,EAAcF,aAAcA,KAIhD,OAAOJ,EAAYkB,eAAaL,EAAQb,GAAaa,MCtE3CM,WCHIC,MDGhB,SAAYD,GACVA,wBACAA,sBACAA,oBACAA,0BACAA,sBACAA,wCACAA,8CACAA,4CARF,CAAYA,IAAAA,aEDCE,EAAkCC,OAAOC,OAAO,CAC3DC,EAAG,EACHC,EAAG,aCCWC,EAAgBC,EAAiBC,GAC/C,OAAOC,KAAKC,KAAKD,KAAKE,IAAIJ,EAAGH,EAAII,EAAGJ,EAAG,GAAKK,KAAKE,IAAIJ,EAAGF,EAAIG,EAAGH,EAAG,aCHpDO,EACdC,EACAC,GAEA,MAAMC,EAAmBC,sBAAoBH,GAE7C,OAAKE,GAKEA,EAAiBX,EAAIU,EAAKG,MAAQH,EAAKI,MAAS,UAChDH,EAAiBV,EAAIS,EAAKK,KAAOL,EAAKM,OAAU,QAL9C,eCFKC,WACbC,MAAO1B,MAAO2B,OACdD,MAAO1B,MAAO4B,MAEf,OAAOD,EAAIC,WAMGC,WACbH,MAAO1B,MAAO2B,OACdD,MAAO1B,MAAO4B,MAEf,OAAOA,EAAID,WAOGG,SAAmBT,KAACA,EAADE,IAAOA,EAAPC,OAAYA,EAAZF,MAAoBA,KACrD,MAAO,CACL,CACEd,EAAGa,EACHZ,EAAGc,GAEL,CACEf,EAAGa,EAAOC,EACVb,EAAGc,GAEL,CACEf,EAAGa,EACHZ,EAAGc,EAAMC,GAEX,CACEhB,EAAGa,EAAOC,EACVb,EAAGc,EAAMC,aAgBCO,EACdC,EACAC,GAEA,IAAKD,GAAoC,IAAtBA,EAAWE,OAC5B,OAAO,KAGT,MAAOC,GAAkBH,EAEzB,OAAOC,EAAWE,EAAeF,GAAYE,EC9D/C,SAASC,EACPlB,EACAG,EACAE,GAEA,gBAHAF,IAAAA,EAAOH,EAAKG,eACZE,IAAAA,EAAML,EAAKK,KAEJ,CACLf,EAAGa,EAAoB,GAAbH,EAAKI,MACfb,EAAGc,EAAoB,GAAdL,EAAKM,iBCRFa,EACdC,EACAC,GAEA,MAAMhB,EAAMV,KAAK2B,IAAID,EAAOhB,IAAKe,EAAMf,KACjCF,EAAOR,KAAK2B,IAAID,EAAOlB,KAAMiB,EAAMjB,MACnCoB,EAAQ5B,KAAK6B,IAAIH,EAAOlB,KAAOkB,EAAOjB,MAAOgB,EAAMjB,KAAOiB,EAAMhB,OAChEqB,EAAS9B,KAAK6B,IAAIH,EAAOhB,IAAMgB,EAAOf,OAAQc,EAAMf,IAAMe,EAAMd,QAItE,GAAIH,EAAOoB,GAASlB,EAAMoB,EAAQ,CAChC,MAEMC,GANMH,EAAQpB,IACPsB,EAASpB,GAStB,OAAOsB,QAFLD,GAJiBL,EAAOjB,MAAQiB,EAAOf,OACvBc,EAAMhB,MAAQgB,EAAMd,OAGSoB,IAEfE,QAAQ,IAI1C,OAAO,QAOIC,EAAuCC,QAACC,cACnDA,EADmDC,eAEnDA,EAFmDC,oBAGnDA,KAEA,MAAMnB,EAAoC,GAE1C,IAAK,MAAMoB,KAAsBD,EAAqB,CACpD,MAAM1E,GAACA,GAAM2E,EACPlC,EAAOgC,EAAeG,IAAI5E,GAEhC,GAAIyC,EAAM,CACR,MAAMoC,EAAoBjB,EAAqBnB,EAAM+B,GAEjDK,EAAoB,GACtBtB,EAAWuB,KAAK,CACd9E,GAAAA,EACAiD,KAAM,CAAC0B,mBAAAA,EAAoBpD,MAAOsD,MAM1C,OAAOtB,EAAWwB,KAAK3B,ICnDzB,SAAS4B,EAAkBC,EAAoBxC,GAC7C,MAAMK,IAACA,EAADF,KAAMA,EAANsB,OAAYA,EAAZF,MAAoBA,GAASvB,EAEnC,OACEK,GAAOmC,EAAMjD,GAAKiD,EAAMjD,GAAKkC,GAAUtB,GAAQqC,EAAMlD,GAAKkD,EAAMlD,GAAKiC,WCVzDkB,EACdC,EACAC,GAEA,OAAOD,GAASC,EACZ,CACErD,EAAGoD,EAAMvC,KAAOwC,EAAMxC,KACtBZ,EAAGmD,EAAMrC,IAAMsC,EAAMtC,KAEvBlB,WCVUyD,EAAuBC,GACrC,OAAO,SACL7C,8BACG8C,mCAAAA,oBAEH,OAAOA,EAAYC,OACjB,CAACC,EAAKC,SACDD,EACH3C,IAAK2C,EAAI3C,IAAMwC,EAAWI,EAAW1D,EACrCkC,OAAQuB,EAAIvB,OAASoB,EAAWI,EAAW1D,EAC3CY,KAAM6C,EAAI7C,KAAO0C,EAAWI,EAAW3D,EACvCiC,MAAOyB,EAAIzB,MAAQsB,EAAWI,EAAW3D,IAE3C,IAAIU,KAKH,MAAMkD,EAAkBN,EAAuB,YClBtCO,EAAeC,GAC7B,GAAIA,EAAUC,WAAW,aAAc,CACrC,MAAMC,EAAiBF,EAAUG,MAAM,GAAI,GAAGC,MAAM,MAEpD,MAAO,CACLlE,GAAIgE,EAAe,IACnB/D,GAAI+D,EAAe,IACnBG,QAASH,EAAe,GACxBI,QAASJ,EAAe,IAErB,GAAIF,EAAUC,WAAW,WAAY,CAC1C,MAAMC,EAAiBF,EAAUG,MAAM,GAAI,GAAGC,MAAM,MAEpD,MAAO,CACLlE,GAAIgE,EAAe,GACnB/D,GAAI+D,EAAe,GACnBG,QAASH,EAAe,GACxBI,QAASJ,EAAe,IAI5B,OAAO,WCdHK,EAA0B,CAACC,iBAAiB,YAKlCC,EACdC,EACAC,YAAAA,IAAAA,EAAmBJ,GAEnB,IAAI3D,EAAmB8D,EAAQE,wBAE/B,GAAID,EAAQH,gBAAiB,CAC3B,MAAMR,UAACA,EAADa,gBAAYA,GAChBC,YAAUJ,GAASK,iBAAiBL,GAElCV,IACFpD,WCpBJA,EACAoD,EACAa,GAEA,MAAMG,EAAkBjB,EAAeC,GAEvC,IAAKgB,EACH,OAAOpE,EAGT,MAAMyD,OAACA,EAADC,OAASA,EAAQpE,EAAG+E,EAAY9E,EAAG+E,GAAcF,EAEjD9E,EAAIU,EAAKG,KAAOkE,GAAc,EAAIZ,GAAUc,WAAWN,GACvD1E,EACJS,EAAKK,IACLiE,GACC,EAAIZ,GACHa,WAAWN,EAAgBV,MAAMU,EAAgBO,QAAQ,KAAO,IAC9DC,EAAIhB,EAASzD,EAAKI,MAAQqD,EAASzD,EAAKI,MACxCsE,EAAIhB,EAAS1D,EAAKM,OAASoD,EAAS1D,EAAKM,OAE/C,MAAO,CACLF,MAAOqE,EACPnE,OAAQoE,EACRrE,IAAKd,EACLgC,MAAOjC,EAAImF,EACXhD,OAAQlC,EAAImF,EACZvE,KAAMb,GDPGqF,CAAiB3E,EAAMoD,EAAWa,IAI7C,MAAM5D,IAACA,EAADF,KAAMA,EAANC,MAAYA,EAAZE,OAAmBA,EAAnBmB,OAA2BA,EAA3BF,MAAmCA,GAASvB,EAElD,MAAO,CACLK,IAAAA,EACAF,KAAAA,EACAC,MAAAA,EACAE,OAAAA,EACAmB,OAAAA,EACAF,MAAAA,YAYYqD,EAA+Bd,GAC7C,OAAOD,EAAcC,EAAS,CAACF,iBAAiB,aExClCiB,EACdf,EACAgB,GAEA,MAAMC,EAA2B,GA4CjC,OAAKjB,EA1CL,SAASkB,EAAwBC,GAC/B,GAAa,MAATH,GAAiBC,EAAc/D,QAAU8D,EAC3C,OAAOC,EAGT,IAAKE,EACH,OAAOF,EAGT,GACEG,aAAWD,IACc,MAAzBA,EAAKE,mBACJJ,EAAcK,SAASH,EAAKE,kBAI7B,OAFAJ,EAAc1C,KAAK4C,EAAKE,kBAEjBJ,EAGT,IAAKM,gBAAcJ,IAASK,eAAaL,GACvC,OAAOF,EAGT,GAAIA,EAAcK,SAASH,GACzB,OAAOF,EAGT,MAAMQ,EAAgBrB,YAAUJ,GAASK,iBAAiBc,GAQ1D,OANIA,IAASnB,YC1CfA,EACAyB,YAAAA,IAAAA,EAAqCrB,YAAUJ,GAASK,iBACtDL,IAGF,MAAM0B,EAAgB,wBAGtB,MAFmB,CAAC,WAAY,YAAa,aAE3BC,KAAM1E,IACtB,MAAMjC,EAAQyG,EAAcxE,GAE5B,MAAwB,iBAAVjC,GAAqB0G,EAAcE,KAAK5G,KDgChD6G,CAAaV,EAAMM,IACrBR,EAAc1C,KAAK4C,YE5CzBA,EACAM,GAEA,gBAFAA,IAAAA,EAAqCrB,YAAUe,GAAMd,iBAAiBc,IAEpC,UAA3BM,EAAcK,SF6CfC,CAAQZ,EAAMM,GACTR,EAGFC,EAAwBC,EAAKa,YAO/Bd,CAAwBlB,GAHtBiB,WAMKgB,EAA2Bd,GACzC,MAAOe,GAA2BnB,EAAuBI,EAAM,GAE/D,aAAOe,EAAAA,EAA2B,cG3DpBC,EAAqBnC,GACnC,OAAKoC,aAAcpC,EAIfqC,WAASrC,GACJA,EAGJsC,SAAOtC,GAKVoB,aAAWpB,IACXA,IAAYuC,mBAAiBvC,GAASqB,iBAE/BmB,OAGLjB,gBAAcvB,GACTA,EAGF,KAdE,KARA,cCPKyC,EAAqBzC,GACnC,OAAIqC,WAASrC,GACJA,EAAQ0C,QAGV1C,EAAQ2C,oBAGDC,EAAqB5C,GACnC,OAAIqC,WAASrC,GACJA,EAAQ6C,QAGV7C,EAAQ8C,mBAGDC,EACd/C,GAEA,MAAO,CACLxE,EAAGiH,EAAqBzC,GACxBvE,EAAGmH,EAAqB5C,QCzBhBgD,WCEIC,EAA2BjD,GACzC,SAAKoC,cAAcpC,IAIZA,IAAYkD,SAAS7B,0BCLd8B,EAAkBC,GAChC,MAAMC,EAAY,CAChB7H,EAAG,EACHC,EAAG,GAEC6H,EAAaL,EAA2BG,GAC1C,CACE5G,OAAQgG,OAAOe,YACfjH,MAAOkG,OAAOgB,YAEhB,CACEhH,OAAQ4G,EAAmBK,aAC3BnH,MAAO8G,EAAmBM,aAE1BC,EAAY,CAChBnI,EAAG4H,EAAmBQ,YAAcN,EAAWhH,MAC/Cb,EAAG2H,EAAmBS,aAAeP,EAAW9G,QAQlD,MAAO,CACLsH,MANYV,EAAmBN,WAAaO,EAAU5H,EAOtDsI,OANaX,EAAmBT,YAAcU,EAAU7H,EAOxDwI,SANeZ,EAAmBN,WAAaa,EAAUlI,EAOzDwI,QANcb,EAAmBT,YAAcgB,EAAUnI,EAOzDmI,UAAAA,EACAN,UAAAA,IFhCJ,SAAYL,GACVA,yBACAA,4BAFF,CAAYA,IAAAA,aGMNkB,EAAmB,CACvB1I,EAAG,GACHC,EAAG,aAGW0I,EACdC,EACAC,IAEAC,EACAC,OAFAhI,IAACA,EAADF,KAAMA,EAANoB,MAAYA,EAAZE,OAAmBA,cACnB2G,IAAAA,EAAe,aACfC,IAAAA,EAAsBL,GAEtB,MAAMJ,MAACA,EAADE,SAAQA,EAARD,OAAkBA,EAAlBE,QAA0BA,GAAWd,EAAkBiB,GAEvDI,EAAY,CAChBhJ,EAAG,EACHC,EAAG,GAECgJ,EAAQ,CACZjJ,EAAG,EACHC,EAAG,GAECiJ,EACIL,EAAoB7H,OAAS+H,EAAoB9I,EADrDiJ,EAEGL,EAAoB/H,MAAQiI,EAAoB/I,EA2CzD,OAxCKsI,GAASvH,GAAO8H,EAAoB9H,IAAMmI,GAE7CF,EAAU/I,EAAIuH,EAAU2B,SACxBF,EAAMhJ,EACJ6I,EACAzI,KAAK+I,KACFP,EAAoB9H,IAAMmI,EAAmBnI,GAAOmI,KAGxDV,GACDrG,GAAU0G,EAAoB1G,OAAS+G,IAGvCF,EAAU/I,EAAIuH,EAAU6B,QACxBJ,EAAMhJ,EACJ6I,EACAzI,KAAK+I,KACFP,EAAoB1G,OAAS+G,EAAmB/G,GAC/C+G,KAIHT,GAAWxG,GAAS4G,EAAoB5G,MAAQiH,GAEnDF,EAAUhJ,EAAIwH,EAAU6B,QACxBJ,EAAMjJ,EACJ8I,EACAzI,KAAK+I,KACFP,EAAoB5G,MAAQiH,EAAkBjH,GAASiH,KAElDX,GAAU1H,GAAQgI,EAAoBhI,KAAOqI,IAEvDF,EAAUhJ,EAAIwH,EAAU2B,SACxBF,EAAMjJ,EACJ8I,EACAzI,KAAK+I,KACFP,EAAoBhI,KAAOqI,EAAkBrI,GAAQqI,IAIrD,CACLF,UAAAA,EACAC,MAAAA,YC3EYK,EAAqB9E,GACnC,GAAIA,IAAYkD,SAAS7B,iBAAkB,CACzC,MAAMmC,WAACA,EAADD,YAAaA,GAAef,OAElC,MAAO,CACLjG,IAAK,EACLF,KAAM,EACNoB,MAAO+F,EACP7F,OAAQ4F,EACRjH,MAAOkH,EACPhH,OAAQ+G,GAIZ,MAAMhH,IAACA,EAADF,KAAMA,EAANoB,MAAYA,EAAZE,OAAmBA,GAAUqC,EAAQE,wBAE3C,MAAO,CACL3D,IAAAA,EACAF,KAAAA,EACAoB,MAAAA,EACAE,OAAAA,EACArB,MAAO0D,EAAQ0D,YACflH,OAAQwD,EAAQyD,uBCZJsB,EAAiBC,GAC/B,OAAOA,EAAoB/F,OAAoB,CAACC,EAAKiC,IAC5C8D,MAAI/F,EAAK6D,EAAqB5B,IACpC9F,YCTW6J,EACdlF,EACAmF,GAEA,YAFAA,IAAAA,EAA6CpF,IAExCC,EACH,OAGF,MAAMzD,IAACA,EAADF,KAAMA,EAANsB,OAAYA,EAAZF,MAAoBA,GAAS0H,EAAQnF,GACXiC,EAA2BjC,KAOzDrC,GAAU,GACVF,GAAS,GACTlB,GAAOiG,OAAOe,aACdlH,GAAQmG,OAAOgB,aAEfxD,EAAQoF,eAAe,CACrBC,MAAO,SACPC,OAAQ,WCnBd,MAAMC,EAAa,CACjB,CAAC,IAAK,CAAC,OAAQ,kBFOgBP,GAC/B,OAAOA,EAAoB/F,OAAe,CAACC,EAAKiC,IACvCjC,EAAMuD,EAAqBtB,GACjC,KETH,CAAC,IAAK,CAAC,MAAO,mBFYiB6D,GAC/B,OAAOA,EAAoB/F,OAAe,CAACC,EAAKiC,IACvCjC,EAAM0D,EAAqBzB,GACjC,MEZL,MAAaqE,EACXC,YAAYvJ,EAAkB8D,QAyBtB9D,iBAEDI,kBAEAE,mBAIAD,gBAEAoB,mBAEAF,kBAEApB,YAtCL,MAAM2I,EAAsBjE,EAAuBf,GAC7C0F,EAAgBX,EAAiBC,GAEvCW,KAAKzJ,KAAO,IAAIA,GAChByJ,KAAKrJ,MAAQJ,EAAKI,MAClBqJ,KAAKnJ,OAASN,EAAKM,OAEnB,IAAK,MAAOoJ,EAAMC,EAAMC,KAAoBP,EAC1C,IAAK,MAAMQ,KAAOF,EAChBvK,OAAO0K,eAAeL,KAAMI,EAAK,CAC/B1H,IAAK,KACH,MAAM4H,EAAiBH,EAAgBd,GAGvC,OAAOW,KAAKzJ,KAAK6J,IAFWL,EAAcE,GAAQK,IAIpDC,YAAY,IAKlB5K,OAAO0K,eAAeL,KAAM,OAAQ,CAACO,YAAY,WCpCxCC,EAOXV,YAAoBlI,QAAAA,mBANZ6I,UAIF,QAaCC,UAAY,KACjBV,KAAKS,UAAUE,QAASvN,IAAD,MAAA,gBACrB4M,KAAKpI,eAALgJ,EAAaC,uBAAuBzN,MAbpB4M,YAAApI,EAEb0H,IACLwB,EACAC,EACAzG,uBAEK1C,WAAQoJ,iBAAiBF,EAAWC,EAA0BzG,GACnE0F,KAAKS,UAAU7H,KAAK,CAACkI,EAAWC,EAA0BzG,cCb9C2G,EACdC,EACAC,GAEA,MAAMC,EAAKlL,KAAK+I,IAAIiC,EAAMrL,GACpBwL,EAAKnL,KAAK+I,IAAIiC,EAAMpL,GAE1B,MAA2B,iBAAhBqL,EACFjL,KAAKC,KAAKiL,GAAM,EAAIC,GAAM,GAAKF,EAGpC,MAAOA,GAAe,MAAOA,EACxBC,EAAKD,EAAYtL,GAAKwL,EAAKF,EAAYrL,EAG5C,MAAOqL,EACFC,EAAKD,EAAYtL,EAGtB,MAAOsL,GACFE,EAAKF,EAAYrL,MCtBhBwL,ECGAC,WDOIC,EAAelL,GAC7BA,EAAMkL,0BAGQC,EAAgBnL,GAC9BA,EAAMmL,mBAfR,SAAYH,GACVA,gBACAA,wBACAA,oBACAA,4BACAA,kBACAA,oCACAA,sCAPF,CAAYA,IAAAA,QCGAC,EAAAA,uBAAAA,wCAEVA,mBACAA,qBACAA,mBACAA,eACAA,eACAA,gBACAA,kBCTWG,EAAsC,CACjDC,MAAO,CAACJ,qBAAaK,MAAOL,qBAAaM,OACzCC,OAAQ,CAACP,qBAAaQ,KACtBC,IAAK,CAACT,qBAAaK,MAAOL,qBAAaM,MAAON,qBAAaU,MAGhDC,GAA4D,CACvE5L,WACA6L,mBAACA,KAED,OAAQ7L,EAAM8L,MACZ,KAAKb,qBAAac,MAChB,MAAO,IACFF,EACHtM,EAAGsM,EAAmBtM,EAAI,IAE9B,KAAK0L,qBAAae,KAChB,MAAO,IACFH,EACHtM,EAAGsM,EAAmBtM,EAAI,IAE9B,KAAK0L,qBAAagB,KAChB,MAAO,IACFJ,EACHrM,EAAGqM,EAAmBrM,EAAI,IAE9B,KAAKyL,qBAAaiB,GAChB,MAAO,IACFL,EACHrM,EAAGqM,EAAmBrM,EAAI,YCQrB2M,GAMX3C,YAAoB4C,QAAAA,kBALbC,mBAAoB,OACnBC,iCACAnC,sBACAoC,uBAEY7C,WAAA0C,EAClB,MACEpM,OAAOsB,OAACA,IACN8K,EAEJ1C,KAAK0C,MAAQA,EACb1C,KAAKS,UAAY,IAAID,EAAU5D,mBAAiBhF,IAChDoI,KAAK6C,gBAAkB,IAAIrC,EAAU/F,YAAU7C,IAC/CoI,KAAK8C,cAAgB9C,KAAK8C,cAAcC,KAAK/C,MAC7CA,KAAKgD,aAAehD,KAAKgD,aAAaD,KAAK/C,MAE3CA,KAAKiD,SAGCA,SACNjD,KAAKkD,cAELlD,KAAK6C,gBAAgBvD,IAAIgC,EAAU6B,OAAQnD,KAAKgD,cAChDhD,KAAK6C,gBAAgBvD,IAAIgC,EAAU8B,iBAAkBpD,KAAKgD,cAE1DK,WAAW,IAAMrD,KAAKS,UAAUnB,IAAIgC,EAAUgC,QAAStD,KAAK8C,gBAGtDI,cACN,MAAMK,WAACA,EAADC,QAAaA,GAAWxD,KAAK0C,MAC7BlH,EAAO+H,EAAW/H,KAAKiI,QAEzBjI,GACF+D,EAAuB/D,GAGzBgI,EAAQ9N,GAGFoN,cAAcxM,GACpB,GAAIoN,kBAAgBpN,GAAQ,CAC1B,MAAMzC,OAACA,EAAD8P,QAASA,EAATrJ,QAAkBA,GAAW0F,KAAK0C,OAClCkB,cACJA,EAAgBlC,EADZmC,iBAEJA,EAAmB3B,GAFf4B,eAGJA,EAAiB,UACfxJ,GACE8H,KAACA,GAAQ9L,EAEf,GAAIsN,EAAc5B,IAAIrG,SAASyG,GAE7B,YADApC,KAAK+D,UAAUzN,GAIjB,GAAIsN,EAAc9B,OAAOnG,SAASyG,GAEhC,YADApC,KAAKgD,aAAa1M,GAIpB,MAAMgC,cAACA,GAAiBqL,EAAQF,QAC1BtB,EAAqB7J,EACvB,CAACzC,EAAGyC,EAAc5B,KAAMZ,EAAGwC,EAAc1B,KACzClB,EAECsK,KAAK4C,uBACR5C,KAAK4C,qBAAuBT,GAG9B,MAAM6B,EAAiBH,EAAiBvN,EAAO,CAC7CzC,OAAAA,EACA8P,QAASA,EAAQF,QACjBtB,mBAAAA,IAGF,GAAI6B,EAAgB,CAClB,MAAMC,EAAmBC,WACvBF,EACA7B,GAEIgC,EAAc,CAClBtO,EAAG,EACHC,EAAG,IAECuJ,oBAACA,GAAuBsE,EAAQF,QAEtC,IAAK,MAAMhF,KAAmBY,EAAqB,CACjD,MAAMR,EAAYvI,EAAM8L,MAClBjE,MAACA,EAADG,QAAQA,EAARF,OAAiBA,EAAjBC,SAAyBA,EAAzBL,UAAmCA,EAAnCN,UAA8CA,GAClDF,EAAkBiB,GACd2F,EAAoBjF,EAAqBV,GAEzC4F,EAAqB,CACzBxO,EAAGK,KAAK6B,IACN8G,IAAc0C,qBAAac,MACvB+B,EAAkBtM,MAAQsM,EAAkBzN,MAAQ,EACpDyN,EAAkBtM,MACtB5B,KAAK2B,IACHgH,IAAc0C,qBAAac,MACvB+B,EAAkB1N,KAClB0N,EAAkB1N,KAAO0N,EAAkBzN,MAAQ,EACvDqN,EAAenO,IAGnBC,EAAGI,KAAK6B,IACN8G,IAAc0C,qBAAagB,KACvB6B,EAAkBpM,OAASoM,EAAkBvN,OAAS,EACtDuN,EAAkBpM,OACtB9B,KAAK2B,IACHgH,IAAc0C,qBAAagB,KACvB6B,EAAkBxN,IAClBwN,EAAkBxN,IAAMwN,EAAkBvN,OAAS,EACvDmN,EAAelO,KAKfwO,EACHzF,IAAc0C,qBAAac,QAAU/D,GACrCO,IAAc0C,qBAAae,OAASlE,EACjCmG,EACH1F,IAAc0C,qBAAagB,OAASlE,GACpCQ,IAAc0C,qBAAaiB,KAAOrE,EAErC,GAAImG,GAAcD,EAAmBxO,IAAMmO,EAAenO,EAAG,CAC3D,MAAM2O,EACJ/F,EAAgBzB,WAAaiH,EAAiBpO,EAC1C4O,EACH5F,IAAc0C,qBAAac,OAC1BmC,GAAwBxG,EAAUnI,GACnCgJ,IAAc0C,qBAAae,MAC1BkC,GAAwB9G,EAAU7H,EAEtC,GAAI4O,IAA8BR,EAAiBnO,EAOjD,YAJA2I,EAAgBiG,SAAS,CACvBhO,KAAM8N,EACNG,SAAUb,IAMZK,EAAYtO,EADV4O,EACchG,EAAgBzB,WAAawH,EAG3C3F,IAAc0C,qBAAac,MACvB5D,EAAgBzB,WAAagB,EAAUnI,EACvC4I,EAAgBzB,WAAaU,EAAU7H,EAG3CsO,EAAYtO,GACd4I,EAAgBmG,SAAS,CACvBlO,MAAOyN,EAAYtO,EACnB8O,SAAUb,IAGd,MACK,GAAIS,GAAcF,EAAmBvO,IAAMkO,EAAelO,EAAG,CAClE,MAAM0O,EACJ/F,EAAgBtB,UAAY8G,EAAiBnO,EACzC2O,EACH5F,IAAc0C,qBAAagB,MAC1BiC,GAAwBxG,EAAUlI,GACnC+I,IAAc0C,qBAAaiB,IAC1BgC,GAAwB9G,EAAU5H,EAEtC,GAAI2O,IAA8BR,EAAiBpO,EAOjD,YAJA4I,EAAgBiG,SAAS,CACvB9N,IAAK4N,EACLG,SAAUb,IAMZK,EAAYrO,EADV2O,EACchG,EAAgBtB,UAAYqH,EAG1C3F,IAAc0C,qBAAagB,KACvB9D,EAAgBtB,UAAYa,EAAUlI,EACtC2I,EAAgBtB,UAAYO,EAAU5H,EAG1CqO,EAAYrO,GACd2I,EAAgBmG,SAAS,CACvBhO,KAAMuN,EAAYrO,EAClB6O,SAAUb,IAId,OAIJ9D,KAAK6E,WACHvO,EACAwO,MACEZ,WAAoBF,EAAgBhE,KAAK4C,sBACzCuB,MAOFU,WAAWvO,EAAcyO,GAC/B,MAAMC,OAACA,GAAUhF,KAAK0C,MAEtBpM,EAAMkL,iBACNwD,EAAOD,GAGDhB,UAAUzN,GAChB,MAAM2O,MAACA,GAASjF,KAAK0C,MAErBpM,EAAMkL,iBACNxB,KAAKkF,SACLD,IAGMjC,aAAa1M,GACnB,MAAM6O,SAACA,GAAYnF,KAAK0C,MAExBpM,EAAMkL,iBACNxB,KAAKkF,SACLC,IAGMD,SACNlF,KAAKS,UAAUC,YACfV,KAAK6C,gBAAgBnC,aCrOzB,SAAS0E,GACPC,GAEA,OAAOC,QAAQD,GAAc,aAAcA,GAG7C,SAASE,GACPF,GAEA,OAAOC,QAAQD,GAAc,UAAWA,GDd7B5C,GA6OJ+C,WAAgD,CACrD,CACE1E,UAAW,YACXC,QAAS,CACPzK,aACAsN,cAACA,EAAgBlC,EAAjB+D,aAAuCA,MACvC5R,OAACA,KAED,MAAMuO,KAACA,GAAQ9L,EAAMoP,YAErB,GAAI9B,EAAcjC,MAAMhG,SAASyG,GAAO,CACtC,MAAMuD,EAAY9R,EAAO+R,cAAcnC,QAEvC,QAAIkC,GAAarP,EAAMsB,SAAW+N,IAIlCrP,EAAMkL,uBAENiE,GAAAA,EAAe,CAACnP,MAAOA,EAAMoP,cAEtB,IAGT,OAAO,KCzOf,MAAaG,GAUX/F,YACU4C,EACAoD,EACRC,kBAAAA,IAAAA,WC7EFnO,GAQA,MAAMoO,YAACA,GAAevL,YAAU7C,GAEhC,OAAOA,aAAkBoO,EAAcpO,EAASgF,mBAAiBhF,GDmE9CqO,CAAuBvD,EAAMpM,MAAMsB,cAF5C8K,kBACAoD,mBAXHnD,mBAAoB,OACnBpF,qBACA2I,WAAqB,OACrBC,+BACAC,UAAmC,UACnC3F,sBACA4F,8BACAxD,uBAGE7C,WAAA0C,EACA1C,YAAA8F,EAGR,MAAMxP,MAACA,GAASoM,GACV9K,OAACA,GAAUtB,EAEjB0J,KAAK0C,MAAQA,EACb1C,KAAK8F,OAASA,EACd9F,KAAKzC,SAAWX,mBAAiBhF,GACjCoI,KAAKqG,kBAAoB,IAAI7F,EAAUR,KAAKzC,UAC5CyC,KAAKS,UAAY,IAAID,EAAUuF,GAC/B/F,KAAK6C,gBAAkB,IAAIrC,EAAU/F,YAAU7C,IAC/CoI,KAAKmG,4BAAqB1P,sBAAoBH,MAAUZ,EACxDsK,KAAKkD,YAAclD,KAAKkD,YAAYH,KAAK/C,MACzCA,KAAK6E,WAAa7E,KAAK6E,WAAW9B,KAAK/C,MACvCA,KAAK+D,UAAY/D,KAAK+D,UAAUhB,KAAK/C,MACrCA,KAAKgD,aAAehD,KAAKgD,aAAaD,KAAK/C,MAC3CA,KAAKsG,cAAgBtG,KAAKsG,cAAcvD,KAAK/C,MAC7CA,KAAKuG,oBAAsBvG,KAAKuG,oBAAoBxD,KAAK/C,MAEzDA,KAAKiD,SAGCA,SACN,MAAM6C,OACJA,EACApD,OACEpI,SAASkM,qBAACA,EAADC,2BAAuBA,KAEhCzG,KAeJ,GAbAA,KAAKS,UAAUnB,IAAIwG,EAAOY,KAAKC,KAAM3G,KAAK6E,WAAY,CAAC+B,SAAS,IAChE5G,KAAKS,UAAUnB,IAAIwG,EAAO9D,IAAI2E,KAAM3G,KAAK+D,WAErC+B,EAAOhE,QACT9B,KAAKS,UAAUnB,IAAIwG,EAAOhE,OAAO6E,KAAM3G,KAAKgD,cAG9ChD,KAAK6C,gBAAgBvD,IAAIgC,EAAU6B,OAAQnD,KAAKgD,cAChDhD,KAAK6C,gBAAgBvD,IAAIgC,EAAUuF,UAAWrF,GAC9CxB,KAAK6C,gBAAgBvD,IAAIgC,EAAU8B,iBAAkBpD,KAAKgD,cAC1DhD,KAAK6C,gBAAgBvD,IAAIgC,EAAUwF,YAAatF,GAChDxB,KAAKqG,kBAAkB/G,IAAIgC,EAAUgC,QAAStD,KAAKsG,eAE/CE,EAAsB,CACxB,SACEC,GAAAA,EAA6B,CAC3BnQ,MAAO0J,KAAK0C,MAAMpM,MAClBiN,WAAYvD,KAAK0C,MAAMa,WACvBjJ,QAAS0F,KAAK0C,MAAMpI,UAGtB,OAAO0F,KAAKkD,cAGd,GAAIqC,GAAkBiB,GAMpB,OALAxG,KAAKoG,UAAY/C,WACfrD,KAAKkD,YACLsD,EAAqBO,YAEvB/G,KAAKgH,cAAcR,GAIrB,GAAIpB,GAAqBoB,GAEvB,YADAxG,KAAKgH,cAAcR,GAKvBxG,KAAKkD,cAGCgC,SACNlF,KAAKS,UAAUC,YACfV,KAAK6C,gBAAgBnC,YAIrB2C,WAAWrD,KAAKqG,kBAAkB3F,UAAW,IAEtB,OAAnBV,KAAKoG,YACPa,aAAajH,KAAKoG,WAClBpG,KAAKoG,UAAY,MAIbY,cACN3B,EACA6B,GAEA,MAAMrT,OAACA,EAADsT,UAASA,GAAanH,KAAK0C,MACjCyE,EAAUtT,EAAQwR,EAAYrF,KAAKmG,mBAAoBe,GAGjDhE,cACN,MAAMiD,mBAACA,GAAsBnG,MACvBwD,QAACA,GAAWxD,KAAK0C,MAEnByD,IACFnG,KAAKkG,WAAY,EAGjBlG,KAAKqG,kBAAkB/G,IAAIgC,EAAU8F,MAAO3F,EAAiB,CAC3D4F,SAAS,IAIXrH,KAAKuG,sBAGLvG,KAAKqG,kBAAkB/G,IACrBgC,EAAUgG,gBACVtH,KAAKuG,qBAGP/C,EAAQ2C,IAIJtB,WAAWvO,SACjB,MAAM4P,UAACA,EAADC,mBAAYA,EAAZzD,MAAgCA,GAAS1C,MACzCgF,OACJA,EACA1K,SAASkM,qBAACA,IACR9D,EAEJ,IAAKyD,EACH,OAGF,MAAMpB,WAActO,sBAAoBH,MAAUZ,EAC5CwL,EAAQgD,WAAoBiC,EAAoBpB,GAGtD,IAAKmB,GAAaM,EAAsB,CACtC,GAAIpB,GAAqBoB,GAAuB,CAC9C,GACoC,MAAlCA,EAAqBe,WACrBtG,EAAoBC,EAAOsF,EAAqBe,WAEhD,OAAOvH,KAAKgD,eAGd,GAAI/B,EAAoBC,EAAOsF,EAAqBgB,UAClD,OAAOxH,KAAKkD,cAIhB,OAAIqC,GAAkBiB,IAChBvF,EAAoBC,EAAOsF,EAAqBe,WAC3CvH,KAAKgD,oBAIhBhD,KAAKgH,cAAcR,EAAsBtF,GAIvC5K,EAAMmR,YACRnR,EAAMkL,iBAGRwD,EAAOD,GAGDhB,YACN,MAAM2D,QAACA,EAADzC,MAAUA,GAASjF,KAAK0C,MAE9B1C,KAAKkF,SACAlF,KAAKkG,WACRwB,EAAQ1H,KAAK0C,MAAM7O,QAErBoR,IAGMjC,eACN,MAAM0E,QAACA,EAADvC,SAAUA,GAAYnF,KAAK0C,MAEjC1C,KAAKkF,SACAlF,KAAKkG,WACRwB,EAAQ1H,KAAK0C,MAAM7O,QAErBsR,IAGMmB,cAAchQ,GAChBA,EAAM8L,OAASb,qBAAaQ,KAC9B/B,KAAKgD,eAIDuD,0CACDhJ,SAASoK,mBAAgBC,yBEtQ5B9B,GAA+B,CACnChE,OAAQ,CAAC6E,KAAM,iBACfD,KAAM,CAACC,KAAM,eACb3E,IAAK,CAAC2E,KAAM,oBAODkB,WAAsBhC,GACjC/F,YAAY4C,GACV,MAAMpM,MAACA,GAASoM,EAGVqD,EAAiBnJ,mBAAiBtG,EAAMsB,QAE9CkQ,MAAMpF,EAAOoD,GAAQC,IAPZ8B,GAUJrC,WAAa,CAClB,CACE1E,UAAW,gBACXC,QAAS,YACN2E,YAAapP,MACdmP,aAACA,KAED,SAAKnP,EAAMyR,WAA8B,IAAjBzR,EAAM0R,eAI9BvC,GAAAA,EAAe,CAACnP,MAAAA,IAET,aCjCTwP,GAA+B,CACnCY,KAAM,CAACC,KAAM,aACb3E,IAAK,CAAC2E,KAAM,YAGd,IAAKsB,IAAL,SAAKA,GACHA,+BADF,CAAKA,KAAAA,cAQQC,WAAoBrC,GAC/B/F,YAAY4C,GACVoF,MAAMpF,EAAOoD,GAAQlJ,mBAAiB8F,EAAMpM,MAAMsB,UAFzCsQ,GAKJ1C,WAAa,CAClB,CACE1E,UAAW,cACXC,QAAS,YACN2E,YAAapP,MACdmP,aAACA,KAED,OAAInP,EAAM0R,SAAWC,GAAYE,mBAIjC1C,GAAAA,EAAe,CAACnP,MAAAA,KAET,YC/BTwP,GAA+B,CACnChE,OAAQ,CAAC6E,KAAM,eACfD,KAAM,CAACC,KAAM,aACb3E,IAAK,CAAC2E,KAAM,mBAODyB,WAAoBvC,GAC/B/F,YAAY4C,GACVoF,MAAMpF,EAAOoD,IAuBHuC,eASV,OALAxL,OAAOmE,iBAAiB8E,GAAOY,KAAKC,KAAMlR,EAAM,CAC9C4R,SAAS,EACTT,SAAS,IAGJ,WACL/J,OAAOgE,oBAAoBiF,GAAOY,KAAKC,KAAMlR,IAK/C,SAASA,WCnDD6S,GAmCAC,GDxBCH,GAKJ5C,WAAa,CAClB,CACE1E,UAAW,eACXC,QAAS,YACN2E,YAAapP,MACdmP,aAACA,KAED,MAAM+C,QAACA,GAAWlS,EAElB,QAAIkS,EAAQjR,OAAS,UAIrBkO,GAAAA,EAAe,CAACnP,MAAAA,IAET,QC/BHgS,GAAAA,8BAAAA,yDAEVA,wCAiCUC,GAAAA,yBAAAA,wDAEVA,+CA4IF,MAAME,GAAoC,CACxC5S,EAAG,CAACwS,CAAChL,EAAU2B,WAAW,EAAOqJ,CAAChL,EAAU6B,UAAU,GACtDpJ,EAAG,CAACuS,CAAChL,EAAU2B,WAAW,EAAOqJ,CAAChL,EAAU6B,UAAU,QC/K5CwJ,IAAAA,GAAAA,4BAAAA,qDAEVA,yCACAA,wCAGUC,6BAAAA,sDAYZ,MAAMC,GAAwB,IAAIC,aC3BlBC,GAIdzT,EACA0T,GAEA,OAAOC,cACJC,GACM5T,EAID4T,IAIwB,mBAAdF,EAA2BA,EAAU1T,GAASA,GAPnD,KASX,CAAC0T,EAAW1T,aCXA6T,UAAkBC,SAACA,EAADC,SAAWA,KAC3C,MAAMC,EAAeC,WAASH,GACxBI,EAAiBvU,UACrB,KACE,GACEoU,GACkB,oBAAXvM,aAC0B,IAA1BA,OAAO2M,eAEd,OAGF,MAAMA,eAACA,GAAkB3M,OAEzB,OAAO,IAAI2M,EAAeH,IAG5B,CAACD,IAOH,OAJA7V,YAAU,IACD,UAAMgW,SAAAA,EAAgBE,aAC5B,CAACF,IAEGA,EC3BT,SAASG,GAAerP,GACtB,OAAO,IAAIwF,EAAKzF,EAAcC,GAAUA,GAG1C,SAAgBsP,GACdtP,EACAmF,EACAoK,YADApK,IAAAA,EAAgDkK,IAGhD,MAAOnT,EAAMsT,GAAW9U,WAA4B,MAEpD,SAAS+U,IACPD,EAASE,IACP,IAAK1P,EACH,OAAO,KAG0B,MAAnC,IAA4B,IAAxBA,EAAQ2P,YAGV,sBAAOD,EAAAA,EAAeH,KAAgB,KAGxC,MAAMK,EAAUzK,EAAQnF,GAExB,OAAI6P,KAAKC,UAAUJ,KAAiBG,KAAKC,UAAUF,GAC1CF,EAGFE,IAIX,MAAMG,kBC9B4BjB,SAACA,EAADC,SAAWA,KAC7C,MAAMiB,EAAkBf,WAASH,GAC3BiB,EAAmBpV,UAAQ,KAC/B,GACEoU,GACkB,oBAAXvM,aAC4B,IAA5BA,OAAOyN,iBAEd,OAGF,MAAMA,iBAACA,GAAoBzN,OAE3B,OAAO,IAAIyN,EAAiBD,IAC3B,CAACA,EAAiBjB,IAMrB,OAJA7V,YAAU,IACD,UAAM6W,SAAAA,EAAkBX,aAC9B,CAACW,IAEGA,EDUkBG,CAAoB,CAC3CpB,SAASqB,GACP,GAAKnQ,EAIL,IAAK,MAAMoQ,KAAUD,EAAS,CAC5B,MAAME,KAACA,EAAD9S,OAAOA,GAAU6S,EAEvB,GACW,cAATC,GACA9S,aAAkB+S,aAClB/S,EAAOgT,SAASvQ,GAChB,CACAyP,IACA,WAKFP,EAAiBL,GAAkB,CAACC,SAAUW,IAiBpD,OAfAe,4BAA0B,KACxBf,IAEIzP,SACFkP,GAAAA,EAAgBuB,QAAQzQ,SACxB+P,GAAAA,EAAkBU,QAAQvN,SAASwN,KAAM,CACvCC,WAAW,EACXC,SAAS,YAGX1B,GAAAA,EAAgBE,mBAChBW,GAAAA,EAAkBX,eAEnB,CAACpP,IAEG9D,EE1ET,MAAMqS,GAA0B,YCAhBsC,GACdnL,EACAoL,YAAAA,IAAAA,EAAsB,IAEtB,MAAMC,EAAuBC,SAA2B,MAsBxD,OApBA9X,YACE,KACE6X,EAAqB3H,QAAU,MAGjC0H,GAGF5X,YAAU,KACR,MAAM+X,EAAmBvL,IAAkBrK,EAEvC4V,IAAqBF,EAAqB3H,UAC5C2H,EAAqB3H,QAAU1D,IAG5BuL,GAAoBF,EAAqB3H,UAC5C2H,EAAqB3H,QAAU,OAEhC,CAAC1D,IAEGqL,EAAqB3H,QACxB8H,WAASxL,EAAeqL,EAAqB3H,SAC7C/N,WC7BU8V,GAAcnR,GAC5B,OAAOrF,UAAQ,IAAOqF,WCHYA,GAClC,MAAM1D,EAAQ0D,EAAQwD,WAChBhH,EAASwD,EAAQuD,YAEvB,MAAO,CACLhH,IAAK,EACLF,KAAM,EACNoB,MAAOnB,EACPqB,OAAQnB,EACRF,MAAAA,EACAE,OAAAA,GDP8B4U,CAAoBpR,GAAW,KAAO,CACpEA,IEIJ,MAAMuO,GAAuB,YCRb8C,GACdlQ,GAEA,IAAKA,EACH,OAAO,KAGT,GAAIA,EAAKmQ,SAASpU,OAAS,EACzB,OAAOiE,EAET,MAAMoQ,EAAapQ,EAAKmQ,SAAS,GAEjC,OAAO/P,gBAAcgQ,GAAcA,EAAapQ,QCFrCqQ,GAAiB,CAC5B,CAACC,OAAQjE,GAAevN,QAAS,IACjC,CAACwR,OAAQrJ,GAAgBnI,QAAS,KAGvByR,GAAuB,CAACtI,QAAS,IAEjCuI,GAAsE,CACjFtY,UAAW,CACT8L,QAASrE,GAEX8Q,UAAW,CACTzM,QAASrE,EACT+Q,SAAUxD,0BAAkByD,cAC5BC,UAAWzD,2BAAmB0D,WAEhCC,YAAa,CACX9M,QAASpF,UCxBAmS,WAA+B1D,IAI1CnQ,IAAI5E,SACF,OAAa,MAANA,YAAagU,MAAMpP,IAAI5E,WAAmB0Y,EAGnDC,UACE,OAAOC,MAAMC,KAAK3M,KAAK4M,UAGzBC,aACE,OAAO7M,KAAKyM,UAAUK,OAAOzU,IAAA,IAAC+Q,SAACA,KAAF,OAAiBA,IAGhD2D,WAAWjZ,WACT,yBAAOkM,KAAKtH,IAAI5E,WAATkZ,EAAcxR,KAAKiI,gBAAW+I,SCf5BS,GAAgD,CAC3DC,eAAgB,KAChBrZ,OAAQ,KACR0P,WAAY,KACZ4J,eAAgB,KAChB9V,WAAY,KACZ+V,kBAAmB,KACnBC,eAAgB,IAAIxE,IACpBtQ,eAAgB,IAAIsQ,IACpBrQ,oBAAqB,IAAI+T,GACzBvY,KAAM,KACNsY,YAAa,CACXgB,QAAS,CACP7J,QAAS,MAEXlN,KAAM,KACNgX,OAAQ9X,GAEV4J,oBAAqB,GACrBmO,wBAAyB,GACzBC,uBAAwBzB,GACxB0B,2BAA4BjY,EAC5BkY,WAAY,KACZC,oBAAoB,GAGTC,GAAoD,CAC/DX,eAAgB,KAChB1H,WAAY,GACZ3R,OAAQ,KACRsZ,eAAgB,KAChBW,kBAAmB,CACjBpa,UAAW,IAEbqa,SAAUtY,EACV4X,eAAgB,IAAIxE,IACpB7U,KAAM,KACN0Z,2BAA4BjY,GAGjBuY,GAAkB9a,gBAC7B2a,IAGWI,GAAgB/a,gBAC3B+Z,aChDciB,KACd,MAAO,CACLxa,UAAW,CACTG,OAAQ,KACRsS,mBAAoB,CAACtQ,EAAG,EAAGC,EAAG,GAC9BqY,MAAO,IAAItF,IACXuF,UAAW,CAACvY,EAAG,EAAGC,EAAG,IAEvBmW,UAAW,CACToC,WAAY,IAAI9B,cAKN+B,GAAQC,EAAcC,GACpC,OAAQA,EAAO9D,MACb,KAAKlV,EAAOqR,UACV,MAAO,IACF0H,EACH7a,UAAW,IACN6a,EAAM7a,UACTyS,mBAAoBqI,EAAOrI,mBAC3BtS,OAAQ2a,EAAO3a,SAGrB,KAAK2B,EAAOiZ,SACV,OAA8B,MAA1BF,EAAM7a,UAAUG,OACX0a,EAGF,IACFA,EACH7a,UAAW,IACN6a,EAAM7a,UACT0a,UAAW,CACTvY,EAAG2Y,EAAOzJ,YAAYlP,EAAI0Y,EAAM7a,UAAUyS,mBAAmBtQ,EAC7DC,EAAG0Y,EAAOzJ,YAAYjP,EAAIyY,EAAM7a,UAAUyS,mBAAmBrQ,KAIrE,KAAKN,EAAOkZ,QACZ,KAAKlZ,EAAOmZ,WACV,MAAO,IACFJ,EACH7a,UAAW,IACN6a,EAAM7a,UACTG,OAAQ,KACRsS,mBAAoB,CAACtQ,EAAG,EAAGC,EAAG,GAC9BsY,UAAW,CAACvY,EAAG,EAAGC,EAAG,KAI3B,KAAKN,EAAOoZ,kBAAmB,CAC7B,MAAMvU,QAACA,GAAWmU,GACZ1a,GAACA,GAAMuG,EACPgU,EAAa,IAAI9B,GAAuBgC,EAAMtC,UAAUoC,YAG9D,OAFAA,EAAWQ,IAAI/a,EAAIuG,GAEZ,IACFkU,EACHtC,UAAW,IACNsC,EAAMtC,UACToC,WAAAA,IAKN,KAAK7Y,EAAOsZ,qBAAsB,CAChC,MAAMhb,GAACA,EAADsM,IAAKA,EAALgJ,SAAUA,GAAYoF,EACtBnU,EAAUkU,EAAMtC,UAAUoC,WAAW3V,IAAI5E,GAE/C,IAAKuG,GAAW+F,IAAQ/F,EAAQ+F,IAC9B,OAAOmO,EAGT,MAAMF,EAAa,IAAI9B,GAAuBgC,EAAMtC,UAAUoC,YAM9D,OALAA,EAAWQ,IAAI/a,EAAI,IACduG,EACH+O,SAAAA,IAGK,IACFmF,EACHtC,UAAW,IACNsC,EAAMtC,UACToC,WAAAA,IAKN,KAAK7Y,EAAOuZ,oBAAqB,CAC/B,MAAMjb,GAACA,EAADsM,IAAKA,GAAOoO,EACZnU,EAAUkU,EAAMtC,UAAUoC,WAAW3V,IAAI5E,GAE/C,IAAKuG,GAAW+F,IAAQ/F,EAAQ+F,IAC9B,OAAOmO,EAGT,MAAMF,EAAa,IAAI9B,GAAuBgC,EAAMtC,UAAUoC,YAG9D,OAFAA,EAAWW,OAAOlb,GAEX,IACFya,EACHtC,UAAW,IACNsC,EAAMtC,UACToC,WAAAA,IAKN,QACE,OAAOE,YCtGGU,UAAa7F,SAACA,KAC5B,MAAMvV,OAACA,EAADqZ,eAASA,EAATG,eAAyBA,GAAkB/Z,aAAW0a,IACtDkB,EAAyBC,cAAYjC,GACrCkC,EAAmBD,oBAAYtb,SAAAA,EAAQC,IAqD7C,OAlDAP,YAAU,KACR,IAAI6V,IAIC8D,GAAkBgC,GAA8C,MAApBE,EAA0B,CACzE,IAAK1L,kBAAgBwL,GACnB,OAGF,GAAI3R,SAAS8R,gBAAkBH,EAAuBtX,OAEpD,OAGF,MAAM0X,EAAgBjC,EAAe3U,IAAI0W,GAEzC,IAAKE,EACH,OAGF,MAAM1J,cAACA,EAADpK,KAAgBA,GAAQ8T,EAE9B,IAAK1J,EAAcnC,UAAYjI,EAAKiI,QAClC,OAGF8L,sBAAsB,KACpB,IAAK,MAAMlV,IAAW,CAACuL,EAAcnC,QAASjI,EAAKiI,SAAU,CAC3D,IAAKpJ,EACH,SAGF,MAAMmV,EAAgBC,yBAAuBpV,GAE7C,GAAImV,EAAe,CACjBA,EAAcE,QACd,YAKP,CACDxC,EACA9D,EACAiE,EACA+B,EACAF,IAGK,cCjEOS,GACdC,SACAjW,UAACA,KAAckW,KAEf,aAAOD,GAAAA,EAAWrY,OACdqY,EAAUtW,OAAkB,CAACwW,EAAa1W,IACjCA,EAAS,CACdO,UAAWmW,KACRD,IAEJlW,GACHA,EC8GC,MAAMoW,GAAyB7c,gBAAyB,IAC1DwC,EACHsE,OAAQ,EACRC,OAAQ,IAGV,IAAK+V,IAAL,SAAKA,GACHA,qCACAA,mCACAA,iCAHF,CAAKA,KAAAA,QAML,MAAaC,GAAaC,QAAK,4BAAoBpc,GACjDA,EADiDqc,cAEjDA,EAFiDC,WAGjDA,GAAa,EAHoCzE,SAIjDA,EAJiD0E,QAKjDA,EAAUxE,GALuCyE,mBAMjDA,EAAqBlY,EAN4BmY,UAOjDA,EAPiDX,UAQjDA,KACGlN,KAEH,MAAM8N,EAAQC,aAAWnC,QAAS9B,EAAW0B,KACtCK,EAAOR,GAAYyC,GACnBE,EAAsBC,cCjJ7B,MAAOlQ,GAAa1L,WAAS,IAAM,IAAI6b,KAEjCvd,EAAmBwd,cACtBzd,IACCqN,EAAUnB,IAAIlM,GACP,IAAMqN,EAAUuO,OAAO5b,IAEhC,CAACqN,IAUH,MAAO,CAPUoQ,cACfxY,QAACqS,KAACA,EAADpU,MAAOA,KACNmK,EAAUE,QAASvN,IAAD,MAAA,gBAAcA,EAASsX,WAAToG,OAAA1d,EAAiBkD,MAEnD,CAACmK,IAGepN,GDiIhB0d,IACKC,EAAQC,GAAalc,WAAiBib,GAAOkB,eAC9CC,EAAgBH,IAAWhB,GAAOoB,aAEtC1d,WAAYG,OAAQwd,EAAUlD,MAAOd,EAA1Be,UAA0CA,GACrDnC,WAAYoC,WAAY7V,IACtB+V,EACE/S,EAAmB,MAAZ6V,EAAmBhE,EAAe3U,IAAI2Y,GAAY,KACzDC,EAAcjG,SAAkC,CACpDkG,QAAS,KACTC,WAAY,OAER3d,EAASmB,UACb,KAAA,MAAA,OACc,MAAZqc,EACI,CACEvd,GAAIud,EAEJta,oBAAMyE,SAAAA,EAAMzE,QAAQgV,GACpBxV,KAAM+a,GAER,MACN,CAACD,EAAU7V,IAEPiW,EAAYpG,SAAgC,OAC3CqG,EAAcC,GAAmB5c,WAAgC,OACjEmY,GAAgB0E,IAAqB7c,WAAuB,MAC7D8c,GAAcC,iBAAepP,EAAO/M,OAAOiX,OAAOlK,IAClDqP,GAAyBnd,+BAA8Bd,GACvDke,GAA6Bhd,UACjC,IAAMwD,EAAoBqU,aAC1B,CAACrU,IAEGiV,GE/KCzY,UACL,MACEtB,UAAW,IACNsY,GAA8BtY,mBAC9Bue,UAAAA,GAAQve,WAEbuY,UAAW,IACND,GAA8BC,mBAC9BgG,UAAAA,GAAQhG,WAEbK,YAAa,IACRN,GAA8BM,qBAC9B2F,UAAAA,GAAQ3F,eAIf,QAlBF2F,GFiLyD1B,UE/JtD0B,GAAQve,gBAAWue,UAAAA,GAAQhG,gBAAWgG,UAAAA,GAAQ3F,kBAlBjD2F,GFkLA,MAAM1Z,eAACA,GAADmV,2BAAiBA,GAAjBE,mBAA6CA,ajBvJnDS,SACA6D,SAACA,EAAD/G,aAAWA,EAAX8G,OAAyBA,KAEzB,MAAOE,EAAOC,GAAYrd,WAAoC,OACxDqX,UAACA,EAAD5M,QAAYA,EAAZ0M,SAAqBA,GAAY+F,EACjCI,EAAgBhH,SAAOgD,GACvBjF,EAsHN,WACE,OAAQ8C,GACN,KAAKxD,0BAAkB4J,OACrB,OAAO,EACT,KAAK5J,0BAAkB6J,eACrB,OAAOL,EACT,QACE,OAAQA,GA7HGM,GACXC,EAAcX,iBAAe1I,GAC7BsE,EAA6BmD,eACjC,SAAC6B,YAAAA,IAAAA,EAA0B,IACrBD,EAAYhP,SAIhB2O,EAAU/c,GACM,OAAVA,EACKqd,EAGFrd,EAAMsd,OAAOD,EAAI5F,OAAQhZ,IAAQuB,EAAMsG,SAAS7H,QAG3D,CAAC2e,IAEGrM,EAAYiF,SAA8B,MAC1C9S,EAAiByQ,cACpBC,IACC,GAAIG,IAAa8I,EACf,OAAOtJ,GAGT,IACGK,GACDA,IAAkBL,IAClByJ,EAAc5O,UAAY4K,GACjB,MAAT8D,EACA,CACA,MAAMS,EAAe,IAAI/J,IAEzB,IAAK,IAAIxU,KAAaga,EAAY,CAChC,IAAKha,EACH,SAGF,GACE8d,GACAA,EAAM5a,OAAS,IACd4a,EAAMxW,SAAStH,EAAUP,KAC1BO,EAAUkC,KAAKkN,QACf,CAEAmP,EAAI/D,IAAIxa,EAAUP,GAAIO,EAAUkC,KAAKkN,SACrC,SAGF,MAAMjI,EAAOnH,EAAUmH,KAAKiI,QACtBlN,EAAOiF,EAAO,IAAIqE,EAAKL,EAAQhE,GAAOA,GAAQ,KAEpDnH,EAAUkC,KAAKkN,QAAUlN,EAErBA,GACFqc,EAAI/D,IAAIxa,EAAUP,GAAIyC,GAI1B,OAAOqc,EAGT,OAAO3J,GAET,CAACoF,EAAY8D,EAAOD,EAAU9I,EAAU5J,IAgD1C,OA7CAjM,YAAU,KACR8e,EAAc5O,QAAU4K,GACvB,CAACA,IAEJ9a,YACE,KACM6V,GAIJsE,KAGF,CAACwE,EAAU9I,IAGb7V,YACE,KACM4e,GAASA,EAAM5a,OAAS,GAC1B6a,EAAS,OAIb,CAAClI,KAAKC,UAAUgI,KAGlB5e,YACE,KAEI6V,GACqB,iBAAdgD,GACe,OAAtBhG,EAAU3C,UAKZ2C,EAAU3C,QAAUJ,WAAW,KAC7BqK,IACAtH,EAAU3C,QAAU,MACnB2I,KAGL,CAACA,EAAWhD,EAAUsE,KAA+BvC,IAGhD,CACL5S,eAAAA,EACAmV,2BAAAA,EACAE,mBAA6B,MAATuE,GiB+BpBU,CAAsBb,GAA4B,CAChDE,SAAUf,EACVhG,aAAc,CAACiD,EAAUvY,EAAGuY,EAAUtY,GACtCmc,OAAQxE,GAAuBxB,YAE7B1I,YGzLN8J,EACAvZ,GAEA,MAAMwb,EAAsB,MAANxb,EAAauZ,EAAe3U,IAAI5E,QAAM0Y,EACtDhR,EAAO8T,EAAgBA,EAAc9T,KAAKiI,QAAU,KAE1D,OAAOuF,cACJ8J,UACC,OAAU,MAANhf,EACK,oBAMF0H,EAAAA,EAAQsX,KAAc,MAE/B,CAACtX,EAAM1H,IHwKUif,CAAc1F,EAAgBgE,GAC3C2B,GAAwBhe,UAC5B,IAAOkY,GAAiBzW,sBAAoByW,IAAkB,KAC9D,CAACA,KAEG+F,GAsiBN,WACE,MAMMC,EACJ/B,MANoC,WAApCO,SAAAA,EAAc/O,uBAEQ,iBAAfyN,GACoB,IAAvBA,EAAW8C,SACI,IAAf9C,GAMN,MAA0B,iBAAfA,EACF,IACFA,EACH8C,QAAAA,GAIG,CAACA,QAAAA,GAzjBgBC,GACpBC,YIjMN5X,EACAgE,GAEA,OAAOsJ,GAAgBtN,EAAMgE,GJ8LC6T,CAC5B9P,GACAkK,GAAuB/Z,UAAU8L,0BKvLY+D,WAC/CA,EAD+C/D,QAE/CA,EAF+C8T,YAG/CA,EAH+CrB,OAI/CA,GAAS,KAET,MAAMsB,EAAclI,UAAO,IACrBxV,EAACA,EAADC,EAAIA,GAAuB,kBAAXmc,EAAuB,CAACpc,EAAGoc,EAAQnc,EAAGmc,GAAUA,EAEtEpH,4BAA0B,KAGxB,IAFkBhV,IAAMC,IAEPyN,EAEf,YADAgQ,EAAY9P,SAAU,GAIxB,GAAI8P,EAAY9P,UAAY6P,EAG1B,OAIF,MAAM9X,QAAO+H,SAAAA,EAAY/H,KAAKiI,QAE9B,IAAKjI,IAA6B,IAArBA,EAAKwO,YAGhB,OAGF,MACMwJ,EAAYxa,EADLwG,EAAQhE,GACgB8X,GAarC,GAXKzd,IACH2d,EAAU3d,EAAI,GAGXC,IACH0d,EAAU1d,EAAI,GAIhByd,EAAY9P,SAAU,EAElBvN,KAAK+I,IAAIuU,EAAU3d,GAAK,GAAKK,KAAK+I,IAAIuU,EAAU1d,GAAK,EAAG,CAC1D,MAAMyG,EAA0BD,EAA2Bd,GAEvDe,GACFA,EAAwBqI,SAAS,CAC/BhO,IAAK4c,EAAU1d,EACfY,KAAM8c,EAAU3d,MAIrB,CAAC0N,EAAY1N,EAAGC,EAAGwd,EAAa9T,ILkInCiU,CAAiC,CAC/BlQ,WAAwB,MAAZ8N,EAAmBhE,EAAe3U,IAAI2Y,GAAY,KAC9DY,OAAQgB,GAAkBS,wBAC1BJ,YAAaF,GACb5T,QAASiO,GAAuB/Z,UAAU8L,UAG5C,MAAM2N,GAAiBxD,GACrBpG,GACAkK,GAAuB/Z,UAAU8L,QACjC4T,IAEIhG,GAAoBzD,GACxBpG,GAAaA,GAAWoQ,cAAgB,MAEpCC,GAAgBvI,SAAsB,CAC1C6B,eAAgB,KAChBrZ,OAAQ,KACR0P,WAAAA,GACAjL,cAAe,KACfjB,WAAY,KACZkB,eAAAA,GACA8U,eAAAA,EACAwG,aAAc,KACdC,iBAAkB,KAClBtb,oBAAAA,EACAxE,KAAM,KACNqL,oBAAqB,GACrB0U,wBAAyB,OAErBC,GAAWxb,EAAoBuU,oBACnC6G,GAAcnQ,QAAQzP,aAAtBigB,EAA4BngB,IAExBwY,mBM/NgC9M,QACtCA,KAEA,MAAOjJ,EAAMsT,GAAW9U,WAA4B,MAkB9CwU,EAAiBL,GAAkB,CAACC,SAjBrB0H,cAClBqD,IACC,IAAK,MAAMtc,OAACA,KAAWsc,EACrB,GAAItY,gBAAchE,GAAS,CACzBiS,EAAStT,IACP,MAAM0T,EAAUzK,EAAQ5H,GAExB,OAAOrB,EACH,IAAIA,EAAMI,MAAOsT,EAAQtT,MAAOE,OAAQoT,EAAQpT,QAChDoT,IAEN,QAIN,CAACzK,MAGG2U,EAAmBtD,cACtBxW,IACC,MAAMmB,EAAOkQ,GAAkBrR,SAE/BkP,GAAAA,EAAgBE,aAEZjO,UACF+N,GAAAA,EAAgBuB,QAAQtP,IAG1BqO,EAAQrO,EAAOgE,EAAQhE,GAAQ,OAEjC,CAACgE,EAAS+J,KAEL+D,EAASC,GAAU6G,aAAWD,GAErC,OAAOnf,UACL,MACEsY,QAAAA,EACA/W,KAAAA,EACAgX,OAAAA,IAEF,CAAChX,EAAM+W,EAASC,INmLE8G,CAAwB,CAC1C7U,QAASiO,GAAuBnB,YAAY9M,UAIxCqU,YAAevH,GAAYgB,QAAQ7J,WAAWF,GAC9CuQ,GAAmB3C,WACrB7E,GAAY/V,QAAQ4W,GACpB,KACEmH,GAAkBhP,QACtBgH,GAAYgB,QAAQ7J,SAAW6I,GAAY/V,MAIvCge,GOjPCvb,EAHoBzC,GPoPQ+d,GAAkB,KAAOnH,GOnPxCrE,GAAgBvS,SADTA,GPuP3B,MAAMoX,GAAanC,GACjBqI,GAAepZ,YAAUoZ,IAAgB,MAIrCxU,YZ1P+B7D,GACrC,MAAMgZ,EAAenJ,SAAO7P,GAEtBiZ,EAAYzL,cACfC,GACMzN,EAKHyN,GACAA,IAAkBL,IAClBpN,GACAgZ,EAAa/Q,SACbjI,EAAKa,aAAemY,EAAa/Q,QAAQpH,WAElC4M,EAGF7N,EAAuBI,GAbrBoN,GAeX,CAACpN,IAOH,OAJAjI,YAAU,KACRihB,EAAa/Q,QAAUjI,GACtB,CAACA,IAEGiZ,EY8NqBC,CAC1BvD,QAAgB6C,GAAAA,GAAYzQ,GAAa,MAErCiK,YRvPNmH,EACAnV,YAAAA,IAAAA,EAA4CpF,GAE5C,MAAOwa,GAAgBD,EACjBhH,EAAanC,GACjBoJ,EAAena,YAAUma,GAAgB,OAEpCC,EAAOC,GAAY/f,WAAuB6T,IAEjD,SAASmM,IACPD,EAAS,IACFH,EAASpd,OAIPod,EAAS/B,IAAKvY,GACnBiD,EAA2BjD,GACtBsT,EACD,IAAI9N,EAAKL,EAAQnF,GAAUA,IANxBuO,IAWb,MAAMW,EAAiBL,GAAkB,CAACC,SAAU4L,IAQpD,OANAlK,4BAA0B,WACxBtB,GAAAA,EAAgBE,aAChBsL,IACAJ,EAAShU,QAAStG,SAAYkP,SAAAA,EAAgBuB,QAAQzQ,KACrD,CAACsa,IAEGE,EQwNyBG,CAAS3V,IAGnC4V,GAAoBtF,GAAeC,EAAW,CAClDjW,UAAW,CACT9D,EAAGuY,EAAUvY,EAAI0e,GAAc1e,EAC/BC,EAAGsY,EAAUtY,EAAIye,GAAcze,EAC/BkE,OAAQ,EACRC,OAAQ,GAEViT,eAAAA,GACArZ,OAAAA,EACAsZ,eAAAA,GACAC,kBAAAA,GACA0G,iBAAAA,GACA9f,KAAM4f,GAAcnQ,QAAQzP,KAC5BkhB,gBAAiB5I,GAAY/V,KAC7B8I,oBAAAA,GACAmO,wBAAAA,GACAG,WAAAA,KAGIwH,GAAqBnC,GACvB1T,MAAI0T,GAAuB5E,GAC3B,KAEErO,YQjRyB4U,GAC/B,MACES,EACAC,GACEtgB,WAAmC,MACjCugB,EAAejK,SAAOsJ,GAGtBY,EAAe1E,cAAava,IAChC,MAAMoF,EAAmBc,EAAqBlG,EAAMsB,QAE/C8D,GAIL2Z,EAAsBD,GACfA,GAILA,EAAkBvG,IAChBnT,EACA0B,EAAqB1B,IAGhB,IAAImN,IAAIuM,IARN,OAUV,IAqDH,OAnDA7hB,YAAU,KACR,MAAMiiB,EAAmBF,EAAa7R,QAEtC,GAAIkR,IAAaa,EAAkB,CACjCC,EAAQD,GAER,MAAMtB,EAAUS,EACb/B,IAAKvY,IACJ,MAAMqb,EAAoBlZ,EAAqBnC,GAE/C,OAAIqb,GACFA,EAAkB1U,iBAAiB,SAAUuU,EAAc,CACzD3O,SAAS,IAGJ,CACL8O,EACAtY,EAAqBsY,KAIlB,OAER5I,OAEGnV,GAIY,MAATA,GAGT0d,EAAqBnB,EAAQ3c,OAAS,IAAIsR,IAAIqL,GAAW,MAEzDoB,EAAa7R,QAAUkR,EAGzB,MAAO,KACLc,EAAQd,GACRc,EAAQD,IAGV,SAASC,EAAQd,GACfA,EAAShU,QAAStG,IAChB,MAAMqb,EAAoBlZ,EAAqBnC,SAE/Cqb,GAAAA,EAAmB7U,oBAAoB,SAAU0U,OAGpD,CAACA,EAAcZ,IAEX3f,UAAQ,IACT2f,EAASpd,OACJ6d,EACH1I,MAAMC,KAAKyI,EAAkBxI,UAAUtT,OACrC,CAACC,EAAKwL,IAAgBzF,MAAI/F,EAAKwL,GAC/BrP,GAEF0J,EAAiBuV,GAGhBjf,EACN,CAACif,EAAUS,IRsLQO,CAAiBtW,IAEjCuW,GAAmB1K,GAAsBnL,IAEzC8V,GAAwB3K,GAAsBnL,GAAe,CACjEoN,KAGI4G,GAA0BzU,MAAI2V,GAAmBW,IAEjDtd,GAAgBwb,GAClBra,EAAgBqa,GAAkBmB,IAClC,KAEE5d,GACJxD,GAAUyE,GACNgY,EAAmB,CACjBzc,OAAAA,EACAyE,cAAAA,GACAC,eAAAA,GACAC,oBAAqBwZ,GACrBmD,mBAAAA,KAEF,KACAW,GAAS1e,EAAkBC,GAAY,OACtCrD,GAAM+hB,IAAWhhB,WAAsB,MAQxC4E,YS3TNA,EACAV,EACAC,GAEA,MAAO,IACFS,EACHK,OAAQf,GAASC,EAAQD,EAAMtC,MAAQuC,EAAMvC,MAAQ,EACrDsD,OAAQhB,GAASC,EAAQD,EAAMpC,OAASqC,EAAMrC,OAAS,GToTvCmf,CAJO1B,GACrBW,GACA3V,MAAI2V,GAAmBY,mBAIzB7hB,UAAAA,GAAMuC,QAAQ,KACd4W,IAGI8I,GAAkB5K,SAA8B,MAChD6K,GAAoBrF,cACxB,CACEva,WACCwV,OAAQqK,EAAT7b,QAAiBA,KAEjB,GAAyB,MAArBmX,EAAUhO,QACZ,OAGF,MAAMF,EAAa8J,EAAe3U,IAAI+Y,EAAUhO,SAEhD,IAAKF,EACH,OAGF,MAAM2J,EAAiB5W,EAAMoP,YAEvB0Q,EAAiB,IAAID,EAAO,CAChCtiB,OAAQ4d,EAAUhO,QAClBF,WAAAA,EACAjN,MAAO4W,EACP5S,QAAAA,EAGAqJ,QAASiQ,GACTlM,QAAQ5T,GAGN,IAFsBuZ,EAAe3U,IAAI5E,GAGvC,OAGF,MAAMuiB,YAACA,GAAexE,GAAYpO,QAC5BnN,EAAwB,CAACxC,GAAAA,SAC/BuiB,GAAAA,EAAc/f,GACdoa,EAAqB,CAAChG,KAAM,cAAepU,MAAAA,KAE7C6Q,UAAUrT,EAAIuR,EAAYc,EAAoBe,GAG5C,IAFsBmG,EAAe3U,IAAI5E,GAGvC,OAGF,MAAMwiB,cAACA,GAAiBzE,GAAYpO,QAC9BnN,EAA0B,CAC9BxC,GAAAA,EACAuR,WAAAA,EACAc,mBAAAA,EACAe,OAAAA,SAGFoP,GAAAA,EAAgBhgB,GAChBoa,EAAqB,CAAChG,KAAM,gBAAiBpU,MAAAA,KAE/CkN,QAAQ2C,GACN,MAAMrS,EAAK2d,EAAUhO,QAErB,GAAU,MAAN3P,EACF,OAGF,MAAMwb,EAAgBjC,EAAe3U,IAAI5E,GAEzC,IAAKwb,EACH,OAGF,MAAM1b,YAACA,GAAeie,GAAYpO,QAC5BnN,EAAwB,CAC5B4W,eAAAA,EACArZ,OAAQ,CAACC,GAAAA,EAAIiD,KAAMuY,EAAcvY,KAAMR,KAAM+a,IAG/CiF,0BAAwB,WACtB3iB,GAAAA,EAAc0C,GACd2a,EAAUjB,GAAOwG,cACjBzI,EAAS,CACPrD,KAAMlV,EAAOqR,UACbV,mBAAAA,EACAtS,OAAQC,IAEV4c,EAAqB,CAAChG,KAAM,cAAepU,MAAAA,IAC3Cqb,EAAgBsE,GAAgBxS,SAChCmO,GAAkB1E,MAGtBlI,OAAOD,GACLgJ,EAAS,CACPrD,KAAMlV,EAAOiZ,SACb1J,YAAAA,KAGJE,MAAOwR,EAAcjhB,EAAOkZ,SAC5BvJ,SAAUsR,EAAcjhB,EAAOmZ,cAKjC,SAAS8H,EAAc/L,GACrB,OAAOgM,iBACL,MAAM7iB,OAACA,EAADwD,WAASA,EAATrD,KAAqBA,EAArB+f,wBAA2BA,GAC/BH,GAAcnQ,QAChB,IAAInN,EAA6B,KAEjC,GAAIzC,GAAUkgB,EAAyB,CACrC,MAAM4C,WAACA,GAAc9E,GAAYpO,QAEjCnN,EAAQ,CACN4W,eAAAA,EACArZ,OAAQA,EACRwD,WAAAA,EACA6J,MAAO6S,EACP/f,KAAAA,GAGE0W,IAASlV,EAAOkZ,SAAiC,mBAAfiI,SACTC,QAAQC,QAAQF,EAAWrgB,MAGpDoU,EAAOlV,EAAOmZ,YAKpB8C,EAAUhO,QAAU,KAEpB8S,0BAAwB,KACtBxI,EAAS,CAACrD,KAAAA,IACVuG,EAAUjB,GAAOkB,eACjB6E,GAAQ,MACRpE,EAAgB,MAChBC,GAAkB,MAClBqE,GAAgBxS,QAAU,KAE1B,MAAM3C,EACJ4J,IAASlV,EAAOkZ,QAAU,YAAc,eAE1C,GAAIpY,EAAO,CACT,MAAMyK,EAAU8Q,GAAYpO,QAAQ3C,SAEpCC,GAAAA,EAAUzK,GACVoa,EAAqB,CAAChG,KAAM5J,EAAWxK,MAAAA,QA7C/C2f,GAAgBxS,QAAU2S,GAoD5B,CAAC/I,IA8CG7H,YUhgBN6K,EACAyG,GAKA,OAAO9hB,UACL,IACEqb,EAAQ/W,OAA2B,CAACwW,EAAahE,KAC/C,MAAOA,OAAQqK,GAAUrK,EAOzB,MAAO,IAAIgE,KALcqG,EAAO3Q,WAAWoN,IAAKjN,KAC9C7E,UAAW6E,EAAU7E,UACrBC,QAAS+V,EAAoBnR,EAAU5E,QAAS+K,QAIjD,IACL,CAACuE,EAASyG,IV8eOC,CACjB1G,EA5CwCQ,cACxC,CACE9P,EACA+K,IAEO,CAACxV,EAAOzC,KACb,MAAM6R,EAAcpP,EAAMoP,YACpBsR,EAAsB3J,EAAe3U,IAAI7E,GAIvB,OAAtB4d,EAAUhO,UAETuT,GAEDtR,EAAYuR,QACZvR,EAAYwR,mBAcS,IANAnW,EACrBzK,EACAwV,EAAOxR,QALiB,CACxBzG,OAAQmjB,MASRtR,EAAYuR,OAAS,CACnBE,WAAYrL,EAAOA,QAGrB2F,EAAUhO,QAAU5P,EACpBqiB,GAAkB5f,EAAOwV,KAI/B,CAACuB,EAAgB6I,gBWjgBU7F,GAC7B9c,YACE,KACE,IAAKkJ,YACH,OAGF,MAAM2a,EAAc/G,EAAQuC,IAAIva,IAAA,IAACyT,OAACA,KAAF,aAAcA,EAAOuL,aAAPvL,EAAOuL,UAErD,MAAO,KACL,IAAK,MAAMC,KAAYF,QACrBE,GAAAA,MAMNjH,EAAQuC,IAAI2E,IAAA,IAACzL,OAACA,KAAF,OAAcA,KXwf5B0L,CAAenH,GAEfxF,4BAA0B,KACpBsC,IAAkB6D,IAAWhB,GAAOwG,cACtCvF,EAAUjB,GAAOoB,cAElB,CAACjE,GAAgB6D,IAEpBzd,YACE,KACE,MAAM0B,WAACA,GAAc4c,GAAYpO,SAC3B5P,OAACA,EAADqZ,eAASA,EAAT7V,WAAyBA,EAAzBrD,KAAqCA,GAAQ4f,GAAcnQ,QAEjE,IAAK5P,IAAWqZ,EACd,OAGF,MAAM5W,EAAuB,CAC3BzC,OAAAA,EACAqZ,eAAAA,EACA7V,WAAAA,EACA6J,MAAO,CACLrL,EAAGke,GAAwBle,EAC3BC,EAAGie,GAAwBje,GAE7B9B,KAAAA,GAGFuiB,0BAAwB,WACtBthB,GAAAA,EAAaqB,GACboa,EAAqB,CAAChG,KAAM,aAAcpU,MAAAA,OAI9C,CAACyd,GAAwBle,EAAGke,GAAwBje,IAGtDvC,YACE,KACE,MAAMM,OACJA,EADIqZ,eAEJA,EAFI7V,WAGJA,EAHImB,oBAIJA,EAJIub,wBAKJA,GACEH,GAAcnQ,QAElB,IACG5P,GACoB,MAArB4d,EAAUhO,UACTyJ,IACA6G,EAED,OAGF,MAAMhgB,WAACA,GAAc8d,GAAYpO,QAC3BgU,EAAgBjf,EAAoBE,IAAIod,IACxC9hB,EACJyjB,GAAiBA,EAAclhB,KAAKkN,QAChC,CACE3P,GAAI2jB,EAAc3jB,GAClByC,KAAMkhB,EAAclhB,KAAKkN,QACzB1M,KAAM0gB,EAAc1gB,KACpBqS,SAAUqO,EAAcrO,UAE1B,KACA9S,EAAuB,CAC3BzC,OAAAA,EACAqZ,eAAAA,EACA7V,WAAAA,EACA6J,MAAO,CACLrL,EAAGke,EAAwBle,EAC3BC,EAAGie,EAAwBje,GAE7B9B,KAAAA,GAGFuiB,0BAAwB,KACtBR,GAAQ/hB,SACRD,GAAAA,EAAauC,GACboa,EAAqB,CAAChG,KAAM,aAAcpU,MAAAA,OAI9C,CAACwf,KAGHjL,4BAA0B,KACxB+I,GAAcnQ,QAAU,CACtByJ,eAAAA,GACArZ,OAAAA,EACA0P,WAAAA,GACAjL,cAAAA,GACAjB,WAAAA,GACAkB,eAAAA,GACA8U,eAAAA,EACAwG,aAAAA,GACAC,iBAAAA,GACAtb,oBAAAA,EACAxE,KAAAA,GACAqL,oBAAAA,GACA0U,wBAAAA,IAGFzC,EAAY7N,QAAU,CACpB8N,QAASuC,GACTtC,WAAYlZ,KAEb,CACDzE,EACA0P,GACAlM,GACAiB,GACA+U,EACAwG,GACAC,GACAvb,GACAC,EACAxE,GACAqL,GACA0U,qBlBjlB4BpV,aAC9BA,EAD8BgH,UAE9BA,EAAY2C,4BAAoBoP,QAFFC,UAG9BA,EAH8BC,aAI9BA,EAJ8B1E,QAK9BA,EAL8B2E,SAM9BA,EAAW,EANmBC,MAO9BA,EAAQvP,uBAAewP,UAPO5C,mBAQ9BA,EAR8B9V,oBAS9BA,EAT8BmO,wBAU9BA,EAV8BtM,MAW9BA,EAX8BnC,UAY9BA,KAEA,MAAMiZ,EA2HR,gBAAyB9W,MACvBA,EADuBkI,SAEvBA,KAKA,MAAM6O,EAAgB9I,cAAYjO,GAElC,OAAO8H,cACJkP,IACC,GAAI9O,IAAa6O,IAAkBC,EAEjC,OAAOzP,GAGT,MAAM5J,EACD3I,KAAKiiB,KAAKjX,EAAMrL,EAAIoiB,EAAcpiB,GADjCgJ,EAED3I,KAAKiiB,KAAKjX,EAAMpL,EAAImiB,EAAcniB,GAIvC,MAAO,CACLD,EAAG,CACDwS,CAAChL,EAAU2B,UACTkZ,EAAeriB,EAAEwH,EAAU2B,YAA8B,IAAjBH,EAC1CwJ,CAAChL,EAAU6B,SACTgZ,EAAeriB,EAAEwH,EAAU6B,UAA4B,IAAhBL,GAE3C/I,EAAG,CACDuS,CAAChL,EAAU2B,UACTkZ,EAAepiB,EAAEuH,EAAU2B,YAA8B,IAAjBH,EAC1CwJ,CAAChL,EAAU6B,SACTgZ,EAAepiB,EAAEuH,EAAU6B,UAA4B,IAAhBL,KAI/C,CAACuK,EAAUlI,EAAO+W,IAhKCG,CAAgB,CAAClX,MAAAA,EAAOkI,UAAW8J,KACjDmF,EAAuBC,GAA2BC,gBACnDC,EAAcnN,SAAoB,CAACxV,EAAG,EAAGC,EAAG,IAC5C2iB,EAAkBpN,SAAwB,CAACxV,EAAG,EAAGC,EAAG,IACpDS,EAAOvB,UAAQ,KACnB,OAAQ2Q,GACN,KAAK2C,4BAAoBoP,QACvB,OAAOvC,EACH,CACEve,IAAKue,EAAmBrf,EACxBkC,OAAQmd,EAAmBrf,EAC3BY,KAAMye,EAAmBtf,EACzBiC,MAAOqd,EAAmBtf,GAE5B,KACN,KAAKyS,4BAAoBoQ,cACvB,OAAOd,IAEV,CAACjS,EAAWiS,EAAczC,IACvBwD,EAAqBtN,SAAuB,MAC5C+E,EAAaS,cAAY,KAC7B,MAAMpS,EAAkBka,EAAmBlV,QAEtChF,GAOLA,EAAgBmG,SAHG4T,EAAY/U,QAAQ5N,EAAI4iB,EAAgBhV,QAAQ5N,EACjD2iB,EAAY/U,QAAQ3N,EAAI2iB,EAAgBhV,QAAQ3N,IAGjE,IACG8iB,EAA4B5jB,UAChC,IACE8iB,IAAUvP,uBAAewP,UACrB,IAAI1Y,GAAqBwZ,UACzBxZ,EACN,CAACyY,EAAOzY,IAGV9L,YACE,KACE,GAAK2f,GAAY7T,EAAoB9H,QAAWhB,EAAhD,CAKA,IAAK,MAAMkI,KAAmBma,EAA2B,CACvD,IAAqC,WAAjCjB,SAAAA,EAAYlZ,IACd,SAGF,MAAMqa,EAAQzZ,EAAoBtE,QAAQ0D,GACpCC,EAAsB8O,EAAwBsL,GAEpD,IAAKpa,EACH,SAGF,MAAMG,UAACA,EAADC,MAAYA,GAASN,EACzBC,EACAC,EACAnI,EACAoI,EACAI,GAGF,IAAK,MAAMkB,IAAQ,CAAC,IAAK,KAClB+X,EAAa/X,GAAMpB,EAAUoB,MAChCnB,EAAMmB,GAAQ,EACdpB,EAAUoB,GAAQ,GAItB,GAAInB,EAAMjJ,EAAI,GAAKiJ,EAAMhJ,EAAI,EAS3B,OARAwiB,IAEAK,EAAmBlV,QAAUhF,EAC7B4Z,EAAsBjI,EAAYyH,GAElCW,EAAY/U,QAAU3E,OACtB2Z,EAAgBhV,QAAU5E,GAM9B2Z,EAAY/U,QAAU,CAAC5N,EAAG,EAAGC,EAAG,GAChC2iB,EAAgBhV,QAAU,CAAC5N,EAAG,EAAGC,EAAG,GACpCwiB,SA9CEA,KAiDJ,CACE3Z,EACAyR,EACAuH,EACAW,EACApF,EACA2E,EAEA3N,KAAKC,UAAU5T,GAEf2T,KAAKC,UAAU6N,GACfK,EACAhZ,EACAuZ,EACApL,EAEAtD,KAAKC,UAAUpL,KkB0dnBga,CAAgB,IACX9F,GACH/R,MAAOkN,EACPwJ,aAActf,GACd6c,mBAAAA,GACA9V,oBAAAA,GACAmO,wBAAAA,KAGF,MAAMwL,GAAgBhkB,UAAQ,KACa,CACvCnB,OAAAA,EACA0P,WAAAA,GACA4J,eAAAA,GACAD,eAAAA,GACA7V,WAAAA,GACA+V,kBAAAA,GACAd,YAAAA,GACAe,eAAAA,EACA7U,oBAAAA,EACAD,eAAAA,GACAvE,KAAAA,GACA0Z,2BAAAA,GACArO,oBAAAA,GACAmO,wBAAAA,GACAC,uBAAAA,GACAG,mBAAAA,GACAD,WAAAA,KAID,CACD9Z,EACA0P,GACA4J,GACAD,GACA7V,GACA+V,GACAd,GACAe,EACA7U,EACAD,GACAvE,GACA0Z,GACArO,GACAmO,GACAC,GACAG,GACAD,KAGIsL,GAAkBjkB,UAAQ,KACa,CACzCkY,eAAAA,GACA1H,WAAAA,GACA3R,OAAAA,EACAsZ,eAAAA,GACAW,kBAAmB,CACjBpa,UAAWqe,IAEbhE,SAAAA,EACAV,eAAAA,EACArZ,KAAAA,GACA0Z,2BAAAA,KAID,CACDR,GACA1H,GACA3R,EACAsZ,GACAY,EACAgE,GACA1E,EACArZ,GACA0Z,KAGF,OACEvY,gBAAClC,EAAkBimB,UAAS7jB,MAAOsb,GACjCxb,gBAAC6Y,GAAgBkL,UAAS7jB,MAAO4jB,IAC/B9jB,gBAAC8Y,GAAciL,UAAS7jB,MAAO2jB,IAC7B7jB,gBAAC4a,GAAuBmJ,UAAS7jB,MAAOsE,IACrCgS,IAGLxW,gBAAC8Z,IAAa7F,UAA0C,WAAhC+G,SAAAA,EAAegJ,iBAEzChkB,gBAAChB,MACKgc,EACJ7b,wBAAyByd,SYjsB3BqH,GAAclmB,gBAAmB,MAEjCmmB,GAAc,kBCnCJC,KACd,OAAOhmB,aAAW2a,ICuBpB,MAEMsL,GAA8B,CAClCC,QAAS,aCfKC,UAAiBC,UAACA,EAAD/N,SAAYA,KAC3C,MACEgO,EACAC,GACE7kB,WAAoC,OACjCsF,EAASwf,GAAc9kB,WAA6B,MACrD+kB,EAAmB3K,cAAYxD,GAwBrC,OAtBKA,GAAagO,IAAkBG,GAClCF,EAAkBE,GAGpBjP,4BAA0B,KACxB,IAAKxQ,EACH,OAGF,MACMvG,QAAK6lB,SAAAA,EAAgBjX,MAAM5O,GAEtB,aAHC6lB,SAAAA,EAAgBvZ,MAGH,MAANtM,EAKnB8iB,QAAQC,QAAQ6C,EAAU5lB,EAAIuG,IAAU0f,KAAK,KAC3CH,EAAkB,QALlBA,EAAkB,OAOnB,CAACF,EAAWC,EAAgBtf,IAG7BlF,gCACGwW,EACAgO,EAAiBK,eAAaL,EAAgB,CAACM,IAAKJ,IAAe,YCtCpEK,GAA8B,CAClCrkB,EAAG,EACHC,EAAG,EACHkE,OAAQ,EACRC,OAAQ,YAGMkgB,UAAyBxO,SAACA,KACxC,OACExW,gBAAC6Y,GAAgBkL,UAAS7jB,MAAOwY,IAC/B1Y,gBAAC4a,GAAuBmJ,UAAS7jB,MAAO6kB,IACrCvO,UCIHyO,GAAkC,CACtCje,SAAU,QACVke,YAAa,QAGTC,GAAuCpN,GACfxJ,kBAAgBwJ,GAEf,4BAAyBV,EAG3C+N,GAAoBC,aAC/B,GAYEP,SAXAQ,GACEA,EADFvN,eAEEA,EAFF8I,YAGEA,EAHFrK,SAIEA,EAJF+O,UAKEA,EALFnkB,KAMEA,EANFokB,MAOEA,EAPFhhB,UAQEA,EARFihB,WASEA,EAAaN,MAIf,IAAK/jB,EACH,OAAO,KAGT,MAAMskB,EAAyB7E,EAC3Brc,EACA,IACKA,EACHK,OAAQ,EACRC,OAAQ,GAER6gB,EAA0C,IAC3CV,GACHzjB,MAAOJ,EAAKI,MACZE,OAAQN,EAAKM,OACbD,IAAKL,EAAKK,IACVF,KAAMH,EAAKG,KACXiD,UAAWohB,MAAIC,UAAUC,SAASJ,GAClCrgB,gBACEwb,GAAe9I,EACX7W,EACE6W,EACA3W,QAEFiW,EACNoO,WACwB,mBAAfA,EACHA,EAAW1N,GACX0N,KACHD,GAGL,OAAOxlB,EAAM+lB,cACXT,EACA,CACEC,UAAAA,EACAC,MAAOG,EACPb,IAAAA,GAEFtO,KCEOwP,GACX7gB,GAC6BjC,QAACxE,OAACA,EAADyY,YAASA,KACvC,MAAM8O,EAAyC,IACzCN,OAACA,EAADJ,UAASA,GAAapgB,EAE5B,SAAIwgB,GAAAA,EAAQjnB,OACV,IAAK,MAAOuM,EAAK/K,KAAUM,OAAOue,QAAQ4G,EAAOjnB,aACjC2Y,IAAVnX,IAIJ+lB,EAAehb,GAAOvM,EAAO2H,KAAKmf,MAAMU,iBAAiBjb,GACzDvM,EAAO2H,KAAKmf,MAAMW,YAAYlb,EAAK/K,IAIvC,SAAIylB,GAAAA,EAAQxO,YACV,IAAK,MAAOlM,EAAK/K,KAAUM,OAAOue,QAAQ4G,EAAOxO,kBACjCE,IAAVnX,GAIJiX,EAAY9Q,KAAKmf,MAAMW,YAAYlb,EAAK/K,GAY5C,aARIqlB,GAAAA,EAAW7mB,QACbA,EAAO2H,KAAK+f,UAAUjc,IAAIob,EAAU7mB,cAGlC6mB,GAAAA,EAAWpO,aACbA,EAAY9Q,KAAK+f,UAAUjc,IAAIob,EAAUpO,aAGpC,WACL,IAAK,MAAOlM,EAAK/K,KAAUM,OAAOue,QAAQkH,GACxCvnB,EAAO2H,KAAKmf,MAAMW,YAAYlb,EAAK/K,SAGjCqlB,GAAAA,EAAW7mB,QACbA,EAAO2H,KAAK+f,UAAUC,OAAOd,EAAU7mB,UAgBhC4nB,GAAoE,CAC/EC,SAAU,IACVC,OAAQ,OACRC,UAdgDrE,IAAA,IAChD5d,WAAW4X,QAACA,EAADsK,MAAUA,MAD2B,MAE5C,CACJ,CACEliB,UAAWohB,MAAIC,UAAUC,SAAS1J,IAEpC,CACE5X,UAAWohB,MAAIC,UAAUC,SAASY,MAQpCC,YAAaX,GAAgC,CAC3CL,OAAQ,CACNjnB,OAAQ,CACNkoB,QAAS,SCxJjB,IAAI3b,GAAM,WAEM4b,GAAOloB,GACrB,OAAOkB,UAAQ,KACb,GAAU,MAANlB,EAKJ,OADAsM,KACOA,IACN,CAACtM,UCcOmoB,GAAc9mB,EAAM+a,KAC/B7X,QAAC2d,YACCA,GAAc,EADfrK,SAECA,EACAuQ,cAAeC,EAHhBxB,MAICA,EAJDC,WAKCA,EALDhL,UAMCA,EANDwM,eAOCA,EAAiB,MAPlB1B,UAQCA,EARD2B,OASCA,EAAS,OAET,MAAMnP,eACJA,EADIrZ,OAEJA,EAFIsZ,eAGJA,EAHIC,kBAIJA,EAJIC,eAKJA,EALI7U,oBAMJA,EANI8T,YAOJA,EAPItY,KAQJA,EARIyZ,uBASJA,EATIpO,oBAUJA,EAVImO,wBAWJA,EAXIG,WAYJA,GACE2L,KACE3f,EAAYrG,aAAWyc,IACvB3P,EAAM4b,SAAOnoB,SAAAA,EAAQC,IACrBwoB,EAAoB3M,GAAeC,EAAW,CAClD1C,eAAAA,EACArZ,OAAAA,EACAsZ,eAAAA,EACAC,kBAAAA,EACA0G,iBAAkBxH,EAAY/V,KAC9BvC,KAAAA,EACAkhB,gBAAiB5I,EAAY/V,KAC7B8I,oBAAAA,EACAmO,wBAAAA,EACA7T,UAAAA,EACAgU,WAAAA,IAEI2F,EAAcxK,GAAgBqE,GAC9B+O,kBF4FuBjK,OAC/BA,EAD+B5E,eAE/BA,EAF+B7U,oBAG/BA,EAH+BiV,uBAI/BA,KAEA,OAAOnE,WAAoB,CAACxV,EAAI0H,KAC9B,GAAe,OAAXyW,EACF,OAGF,MAAMsK,EAA6ClP,EAAe3U,IAAI5E,GAEtE,IAAKyoB,EACH,OAGF,MAAMhZ,EAAagZ,EAAgB/gB,KAAKiI,QAExC,IAAKF,EACH,OAGF,MAAMiZ,EAAiB9Q,GAAkBlQ,GAEzC,IAAKghB,EACH,OAEF,MAAM7iB,UAACA,GAAac,YAAUe,GAAMd,iBAAiBc,GAC/Cb,EAAkBjB,EAAeC,GAEvC,IAAKgB,EACH,OAGF,MAAM+e,EACc,mBAAXzH,EACHA,EA2BV,SACE3X,GAEA,MAAMohB,SAACA,EAADC,OAAWA,EAAXG,YAAmBA,EAAnBF,UAAgCA,GAAa,IAC9CH,MACAnhB,GAGL,OAAOmiB,QAAC5oB,OAACA,EAADyY,YAASA,EAAT3S,UAAsBA,KAAc+iB,KAC1C,IAAKhB,EAEH,OAGF,MAeMiB,EAAiB,CACrB9mB,EAAG8D,EAAU9D,GAfVyW,EAAY/V,KAAKG,KAAO7C,EAAO0C,KAAKG,MAgBvCZ,EAAG6D,EAAU7D,GAfVwW,EAAY/V,KAAKK,IAAM/C,EAAO0C,KAAKK,KAItCoD,OACuB,IAArBL,EAAUK,OACLnG,EAAO0C,KAAKI,MAAQgD,EAAUK,OAAUsS,EAAY/V,KAAKI,MAC1D,EACNsD,OACuB,IAArBN,EAAUM,OACLpG,EAAO0C,KAAKM,OAAS8C,EAAUM,OAAUqS,EAAY/V,KAAKM,OAC3D,GAQF+lB,EAAqBhB,EAAU,IAChCc,EACH7oB,OAAAA,EACAyY,YAAAA,EACA3S,UAAW,CAAC4X,QAAS5X,EAAWkiB,MAAOc,MAGlCE,GAAiBD,EAClBE,EAAeF,EAAmBA,EAAmBrlB,OAAS,GAEpE,GAAI2S,KAAKC,UAAU0S,KAAmB3S,KAAKC,UAAU2S,GAEnD,OAGF,MAAMrH,QAAUqG,SAAAA,EAAc,CAACjoB,OAAAA,EAAQyY,YAAAA,KAAgBoQ,IACjDhD,EAAYpN,EAAY9Q,KAAKuhB,QAAQH,EAAoB,CAC7DlB,SAAAA,EACAC,OAAAA,EACAqB,KAAM,aAGR,OAAO,IAAIpG,QAASC,IAClB6C,EAAUuD,SAAW,WACnBxH,GAAAA,IACAoB,QAtFEqG,CAA2BjL,GAOjC,OALA1S,EACEgE,EACAkK,EAAuB/Z,UAAU8L,SAG5Bka,EAAU,CACf7lB,OAAQ,CACNC,GAAAA,EACAiD,KAAMwlB,EAAgBxlB,KACtByE,KAAM+H,EACNhN,KAAMkX,EAAuB/Z,UAAU8L,QAAQ+D,IAEjD8J,eAAAA,EACAf,YAAa,CACX9Q,KAAAA,EACAjF,KAAMkX,EAAuBnB,YAAY9M,QAAQgd,IAEnDhkB,oBAAAA,EACAiV,uBAAAA,EACA9T,UAAWgB,MEvJSwiB,CAAiB,CACrClL,OAAQkK,EACR9O,eAAAA,EACA7U,oBAAAA,EACAiV,uBAAAA,IAMF,OACEtY,gBAACglB,QACChlB,gBAACskB,IAAiBC,UAAWwC,GAC1BroB,GAAUuM,EACTjL,gBAAColB,IACCna,IAAKA,EACLtM,GAAID,EAAOC,GACXmmB,IATE3G,EAAchH,EAAYiB,YAASf,EAUrCiO,GAAI2B,EACJlP,eAAgBA,EAChB8I,YAAaA,EACb0E,UAAWA,EACXE,WAAYA,EACZrkB,KAAM+c,EACNqH,MAAO,CACL0B,OAAAA,KACG1B,GAELhhB,UAAW2iB,GAEV3Q,GAED,uMtE9EmCtT,QAACC,cAChDA,EADgDC,eAEhDA,EAFgDC,oBAGhDA,KAEA,MAAM4kB,EAAa3lB,EACjBa,EACAA,EAAc5B,KACd4B,EAAc1B,KAEVS,EAAoC,GAE1C,IAAK,MAAMoB,KAAsBD,EAAqB,CACpD,MAAM1E,GAACA,GAAM2E,EACPlC,EAAOgC,EAAeG,IAAI5E,GAEhC,GAAIyC,EAAM,CACR,MAAM8mB,EAActnB,EAAgB0B,EAAkBlB,GAAO6mB,GAE7D/lB,EAAWuB,KAAK,CAAC9E,GAAAA,EAAIiD,KAAM,CAAC0B,mBAAAA,EAAoBpD,MAAOgoB,MAI3D,OAAOhmB,EAAWwB,KAAK/B,2BuEtCyBuB,QAACC,cACjDA,EADiDC,eAEjDA,EAFiDC,oBAGjDA,KAEA,MAAM8kB,EAAUnmB,EAAmBmB,GAC7BjB,EAAoC,GAE1C,IAAK,MAAMoB,KAAsBD,EAAqB,CACpD,MAAM1E,GAACA,GAAM2E,EACPlC,EAAOgC,EAAeG,IAAI5E,GAEhC,GAAIyC,EAAM,CACR,MAAMgnB,EAAcpmB,EAAmBZ,GACjCinB,EAAYF,EAAQhkB,OAAO,CAACwW,EAAa2N,EAAQ3E,IAC9ChJ,EAAc/Z,EAAgBwnB,EAAYzE,GAAQ2E,GACxD,GACGC,EAAoBxlB,QAAQslB,EAAY,GAAGrlB,QAAQ,IAEzDd,EAAWuB,KAAK,CACd9E,GAAAA,EACAiD,KAAM,CAAC0B,mBAAAA,EAAoBpD,MAAOqoB,MAKxC,OAAOrmB,EAAWwB,KAAK/B,2UrEfwBuB,QAACG,oBAChDA,EADgDD,eAEhDA,EAFgD4c,mBAGhDA,KAEA,IAAKA,EACH,MAAO,GAGT,MAAM9d,EAAoC,GAE1C,IAAK,MAAMoB,KAAsBD,EAAqB,CACpD,MAAM1E,GAACA,GAAM2E,EACPlC,EAAOgC,EAAeG,IAAI5E,GAEhC,GAAIyC,GAAQuC,EAAkBqc,EAAoB5e,GAAO,CAMvD,MACMinB,EADUrmB,EAAmBZ,GACT+C,OAAO,CAACwW,EAAa2N,IACtC3N,EAAc/Z,EAAgBof,EAAoBsI,GACxD,GACGC,EAAoBxlB,QAAQslB,EAAY,GAAGrlB,QAAQ,IAEzDd,EAAWuB,KAAK,CACd9E,GAAAA,EACAiD,KAAM,CAAC0B,mBAAAA,EAAoBpD,MAAOqoB,MAKxC,OAAOrmB,EAAWwB,KAAK/B,qH4DZIhD,GAC3BA,EAD2BiD,KAE3BA,EAF2BqS,SAG3BA,GAAW,EAHgBuU,WAI3BA,KAEA,MAAMvd,EAAMxL,cARI,cASV4Q,WACJA,EADI0H,eAEJA,EAFIrZ,OAGJA,EAHIsZ,eAIJA,EAJIW,kBAKJA,EALIT,eAMJA,EANIrZ,KAOJA,GACEV,aAAW0a,KACT4P,KACJA,EAAOvE,GADHwE,gBAEJA,EAAkB,YAFdC,SAGJA,EAAW,SACTH,EAAAA,EAAc,GACZI,SAAalqB,SAAAA,EAAQC,MAAOA,EAC5B6F,EAA8BrG,aAClCyqB,EAAahO,GAAyBqJ,KAEjC5d,EAAMwiB,GAAc5J,gBACpBxO,EAAeqY,GAAuB7J,eACvC3T,WUvDNA,EACA3M,GAEA,OAAOkB,UAAQ,IACNyL,EAAUnH,OACf,CAACC,WAAKuH,UAACA,EAADC,QAAYA,KAKhB,OAJAxH,EAAIuH,GAAcxK,IAChByK,EAAQzK,EAAOxC,IAGVyF,GAET,IAED,CAACkH,EAAW3M,IVyCGoqB,CAAsB1Y,EAAY1R,GAC9CqqB,EAAUrM,iBAAe/a,GAqC/B,OAnCA8T,4BACE,KACEwC,EAAewB,IAAI/a,EAAI,CAACA,GAAAA,EAAIsM,IAAAA,EAAK5E,KAAAA,EAAMoK,cAAAA,EAAe7O,KAAMonB,IAErD,KACL,MAAM3iB,EAAO6R,EAAe3U,IAAI5E,GAE5B0H,GAAQA,EAAK4E,MAAQA,GACvBiN,EAAe2B,OAAOlb,KAK5B,CAACuZ,EAAgBvZ,IAsBZ,CACLD,OAAAA,EACAqZ,eAAAA,EACAC,eAAAA,EACAwQ,WAvB8C3oB,UAC9C,MACE4oB,KAAAA,EACAE,SAAAA,EACAM,gBAAiBhV,EACjBiV,kBAAgBN,GAAcH,IAASvE,UAAqB7M,EAC5D8R,uBAAwBT,EACxBU,mBAAoBzQ,EAAkBpa,YAExC,CACE0V,EACAwU,EACAE,EACAC,EACAF,EACA/P,EAAkBpa,YASpBqqB,WAAAA,EACAtd,UAAW2I,OAAWoD,EAAY/L,EAClCjF,KAAAA,EACAxH,KAAAA,EACAgqB,WAAAA,EACAC,oBAAAA,EACAtkB,UAAAA,yCErFyB5C,KAC3BA,EAD2BqS,SAE3BA,GAAW,EAFgBtV,GAG3BA,EAH2B0qB,qBAI3BA,KAEA,MAAMpe,EAAMxL,cAZI,cAaVf,OAACA,EAADka,SAASA,EAAT/Z,KAAmBA,EAAnB0Z,2BAAyBA,GAC7Bpa,aAAW0a,IACPyQ,EAAWpT,SAAO,CAACjC,SAAAA,IACnBsV,EAA0BrT,UAAO,GACjC9U,EAAO8U,SAA0B,MACjCsT,EAAatT,SAA8B,OAE/CjC,SAAUwV,EADNC,sBAEJA,EACArF,QAASsF,GACP,IACCvF,MACAiF,GAEC9L,EAAMZ,uBAAe+M,EAAAA,EAAyB/qB,GAwB9CyV,EAAiBL,GAAkB,CACvCC,SAxBmB0H,cACnB,KACO6N,EAAwBjb,SAOH,MAAtBkb,EAAWlb,SACbwD,aAAa0X,EAAWlb,SAG1Bkb,EAAWlb,QAAUJ,WAAW,KAC9BqK,EACEhB,MAAMqS,QAAQrM,EAAIjP,SAAWiP,EAAIjP,QAAU,CAACiP,EAAIjP,UAElDkb,EAAWlb,QAAU,MACpBqb,IAbDJ,EAAwBjb,SAAU,GAgBtC,CAACqb,IAID1V,SAAUwV,IAA2B/qB,IAEjCsgB,EAAmBtD,cACvB,CAACmO,EAAgCC,KAC1B1V,IAID0V,IACF1V,EAAe2V,UAAUD,GACzBP,EAAwBjb,SAAU,GAGhCub,GACFzV,EAAeuB,QAAQkU,KAG3B,CAACzV,KAEI+D,EAAS0Q,GAAc5J,aAAWD,GACnCgK,EAAUrM,iBAAe/a,GAkD/B,OAhDAxD,YAAU,KACHgW,GAAmB+D,EAAQ7J,UAIhC8F,EAAeE,aACfiV,EAAwBjb,SAAU,EAClC8F,EAAeuB,QAAQwC,EAAQ7J,WAC9B,CAAC6J,EAAS/D,IAEbhW,YACE,KACEwa,EAAS,CACPrD,KAAMlV,EAAOoZ,kBACbvU,QAAS,CACPvG,GAAAA,EACAsM,IAAAA,EACAgJ,SAAAA,EACA5N,KAAM8R,EACN/W,KAAAA,EACAQ,KAAMonB,KAIH,IACLpQ,EAAS,CACPrD,KAAMlV,EAAOuZ,oBACb3O,IAAAA,EACAtM,GAAAA,KAIN,CAACA,IAGHP,YAAU,KACJ6V,IAAaqV,EAAShb,QAAQ2F,WAChC2E,EAAS,CACPrD,KAAMlV,EAAOsZ,qBACbhb,GAAAA,EACAsM,IAAAA,EACAgJ,SAAAA,IAGFqV,EAAShb,QAAQ2F,SAAWA,IAE7B,CAACtV,EAAIsM,EAAKgJ,EAAU2E,IAEhB,CACLla,OAAAA,EACA0C,KAAAA,EACA4oB,cAAQnrB,SAAAA,EAAMF,MAAOA,EACrB0H,KAAM8R,EACNtZ,KAAAA,EACAgqB,WAAAA,+BSvJFlS,EACAxR,GAEA,OAAOtF,UACL,MACE8W,OAAAA,EACAxR,cAASA,EAAAA,EAAY,KAGvB,CAACwR,EAAQxR,8DCTR+V,2BAAAA,kBAEH,OAAOrb,UACL,IACE,IAAIqb,GAASvD,OACVhB,GAAsD,MAAVA,GAGjD,IAAIuE"}