{"version": 3, "file": "accessibility.cjs.development.js", "sources": ["../src/components/HiddenText/HiddenText.tsx", "../src/components/LiveRegion/LiveRegion.tsx", "../src/hooks/useAnnouncement.ts"], "sourcesContent": ["import React from 'react';\n\ninterface Props {\n  id: string;\n  value: string;\n}\n\nconst hiddenStyles: React.CSSProperties = {\n  display: 'none',\n};\n\nexport function HiddenText({id, value}: Props) {\n  return (\n    <div id={id} style={hiddenStyles}>\n      {value}\n    </div>\n  );\n}\n", "import React from 'react';\n\nexport interface Props {\n  id: string;\n  announcement: string;\n  ariaLiveType?: \"polite\" | \"assertive\" | \"off\";\n}\n\nexport function LiveRegion({id, announcement, ariaLiveType = \"assertive\"}: Props) {\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap',\n  };\n  \n  return (\n    <div\n      id={id}\n      style={visuallyHidden}\n      role=\"status\"\n      aria-live={ariaLiveType}\n      aria-atomic\n    >\n      {announcement}\n    </div>\n  );\n}\n", "import {useCallback, useState} from 'react';\n\nexport function useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback((value: string | undefined) => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n\n  return {announce, announcement} as const;\n}\n"], "names": ["hiddenStyles", "display", "HiddenText", "id", "value", "React", "style", "LiveRegion", "announcement", "ariaLiveType", "visuallyHidden", "position", "top", "left", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "whiteSpace", "role", "useAnnouncement", "setAnnouncement", "useState", "announce", "useCallback"], "mappings": ";;;;;;;;;AAOA,MAAMA,YAAY,GAAwB;EACxCC,OAAO,EAAE;AAD+B,CAA1C;SAIgBC;MAAW;IAACC,EAAD;IAAKC;;EAC9B,OACEC,4BAAA,MAAA;IAAKF,EAAE,EAAEA;IAAIG,KAAK,EAAEN;GAApB,EACGI,KADH,CADF;AAKD;;SCTeG;MAAW;IAACJ,EAAD;IAAKK,YAAL;IAAmBC,YAAY,GAAG;;;EAE3D,MAAMC,cAAc,GAAwB;IAC1CC,QAAQ,EAAE,OADgC;IAE1CC,GAAG,EAAE,CAFqC;IAG1CC,IAAI,EAAE,CAHoC;IAI1CC,KAAK,EAAE,CAJmC;IAK1CC,MAAM,EAAE,CALkC;IAM1CC,MAAM,EAAE,CAAC,CANiC;IAO1CC,MAAM,EAAE,CAPkC;IAQ1CC,OAAO,EAAE,CARiC;IAS1CC,QAAQ,EAAE,QATgC;IAU1CC,IAAI,EAAE,eAVoC;IAW1CC,QAAQ,EAAE,aAXgC;IAY1CC,UAAU,EAAE;GAZd;EAeA,OACEjB,4BAAA,MAAA;IACEF,EAAE,EAAEA;IACJG,KAAK,EAAEI;IACPa,IAAI,EAAC;iBACMd;;GAJb,EAOGD,YAPH,CADF;AAWD;;SClCegB;EACd,MAAM,CAAChB,YAAD,EAAeiB,eAAf,IAAkCC,cAAQ,CAAC,EAAD,CAAhD;EACA,MAAMC,QAAQ,GAAGC,iBAAW,CAAExB,KAAD;IAC3B,IAAIA,KAAK,IAAI,IAAb,EAAmB;MACjBqB,eAAe,CAACrB,KAAD,CAAf;;GAFwB,EAIzB,EAJyB,CAA5B;EAMA,OAAO;IAACuB,QAAD;IAAWnB;GAAlB;AACD;;;;;;"}