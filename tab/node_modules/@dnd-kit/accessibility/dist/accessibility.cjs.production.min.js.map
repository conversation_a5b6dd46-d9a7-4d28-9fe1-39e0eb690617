{"version": 3, "file": "accessibility.cjs.production.min.js", "sources": ["../src/components/HiddenText/HiddenText.tsx", "../src/components/LiveRegion/LiveRegion.tsx", "../src/hooks/useAnnouncement.ts"], "sourcesContent": ["import React from 'react';\n\ninterface Props {\n  id: string;\n  value: string;\n}\n\nconst hiddenStyles: React.CSSProperties = {\n  display: 'none',\n};\n\nexport function HiddenText({id, value}: Props) {\n  return (\n    <div id={id} style={hiddenStyles}>\n      {value}\n    </div>\n  );\n}\n", "import React from 'react';\n\nexport interface Props {\n  id: string;\n  announcement: string;\n  ariaLiveType?: \"polite\" | \"assertive\" | \"off\";\n}\n\nexport function LiveRegion({id, announcement, ariaLiveType = \"assertive\"}: Props) {\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap',\n  };\n  \n  return (\n    <div\n      id={id}\n      style={visuallyHidden}\n      role=\"status\"\n      aria-live={ariaLiveType}\n      aria-atomic\n    >\n      {announcement}\n    </div>\n  );\n}\n", "import {useCallback, useState} from 'react';\n\nexport function useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback((value: string | undefined) => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n\n  return {announce, announcement} as const;\n}\n"], "names": ["hiddenStyles", "display", "id", "value", "React", "style", "announcement", "ariaLiveType", "position", "top", "left", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "whiteSpace", "role", "setAnnouncement", "useState", "announce", "useCallback"], "mappings": "oJAOA,MAAMA,EAAoC,CACxCC,QAAS,2CAGgBC,GAACA,EAADC,MAAKA,KAC9B,OACEC,uBAAKF,GAAIA,EAAIG,MAAOL,GACjBG,uCCNoBD,GAACA,EAADI,aAAKA,EAALC,aAAmBA,EAAe,eAiB3D,OACEH,uBACEF,GAAIA,EACJG,MAlBwC,CAC1CG,SAAU,QACVC,IAAK,EACLC,KAAM,EACNC,MAAO,EACPC,OAAQ,EACRC,QAAS,EACTC,OAAQ,EACRC,QAAS,EACTC,SAAU,SACVC,KAAM,gBACNC,SAAU,cACVC,WAAY,UAOVC,KAAK,qBACMb,oBAGVD,uCC9BL,MAAOA,EAAce,GAAmBC,WAAS,IAOjD,MAAO,CAACC,SANSC,cAAarB,IACf,MAATA,GACFkB,EAAgBlB,IAEjB,IAEeG,aAAAA"}