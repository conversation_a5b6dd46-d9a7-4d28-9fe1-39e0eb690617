# @babel/template

> Generate an AST from a string template.

See our website [@babel/template](https://babeljs.io/docs/babel-template) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20template%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/template
```

or using yarn:

```sh
yarn add @babel/template --dev
```
