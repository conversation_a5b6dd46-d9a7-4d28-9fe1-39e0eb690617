// WorkSpace Pro - 工作区专用窗口脚本
// 从URL参数获取工作区信息
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        workspaceId: params.get('workspaceId') || '',
        workspaceName: params.get('workspaceName') || '工作区专用窗口',
        tabCount: parseInt(params.get('tabCount') || '0')
    };
}

// 更新页面内容
function updateContent() {
    const params = getUrlParams();
    
    // 更新工作区名称
    const nameElement = document.getElementById('workspaceName');
    if (nameElement) {
        nameElement.textContent = params.workspaceName;
        document.title = `WorkSpace Pro - ${params.workspaceName}`;
    }

    // 更新标签页数量
    const countElement = document.getElementById('tabCount');
    if (countElement) {
        countElement.textContent = params.tabCount.toString();
    }

    // 根据工作区名称设置图标（简单的映射）
    const iconElement = document.getElementById('workspaceIcon');
    if (iconElement) {
        const name = params.workspaceName.toLowerCase();
        if (name.includes('ai') || name.includes('智能')) {
            iconElement.textContent = '🤖';
        } else if (name.includes('开发') || name.includes('dev')) {
            iconElement.textContent = '💻';
        } else if (name.includes('设计') || name.includes('design')) {
            iconElement.textContent = '🎨';
        } else if (name.includes('研究') || name.includes('research')) {
            iconElement.textContent = '🔬';
        } else if (name.includes('生产力') || name.includes('productivity')) {
            iconElement.textContent = '⚡';
        } else {
            iconElement.textContent = '🚀';
        }
    }
}

// 监听URL变化（用于动态更新）
window.addEventListener('popstate', updateContent);

// 初始化
updateContent();

// 更新标签页计数的函数
async function updateTabCount() {
    try {
        if (!chrome || !chrome.tabs) {
            return;
        }

        const tabs = await chrome.tabs.query({ currentWindow: true });

        // 过滤掉占位符页面
        const workspaceTabs = tabs.filter(tab => {
            const url = tab.url || '';
            return !url.includes('workspace-placeholder.html');
        });

        // 更新页面标题以反映标签页数量
        const params = getUrlParams();
        const newTitle = `WorkSpace Pro - ${params.workspaceName} (${workspaceTabs.length} 标签页)`;
        if (document.title !== newTitle) {
            document.title = newTitle;
        }

        // 同时更新标签页列表，确保数据同步
        if (typeof allTabs !== 'undefined' && allTabs.length !== workspaceTabs.length) {
            await loadTabsList();
        }
    } catch (error) {
        console.error('更新标签页计数时出错:', error);
    }
}

// 立即更新一次
updateTabCount();

// 定期检查标签页数量变化（减少检查频率以提高性能）
setInterval(updateTabCount, 2000);

// 监听标签页变化事件以实现实时更新
if (chrome && chrome.tabs) {
    chrome.tabs.onCreated.addListener(() => {
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });

    chrome.tabs.onRemoved.addListener(() => {
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });

    chrome.tabs.onMoved.addListener(() => {
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });

    chrome.tabs.onAttached.addListener(() => {
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });

    chrome.tabs.onDetached.addListener(() => {
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });

    chrome.tabs.onUpdated.addListener(() => {
        if (typeof loadTabsList === 'function') loadTabsList();
    });
}

// 监听Chrome扩展消息以实现实时状态更新
if (chrome && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
        if (message.type === 'USER_TABS_VISIBILITY_CHANGED') {
            console.log('📢 收到用户标签页状态变化消息:', message);

            // 立即更新标签页计数和列表
            updateTabCount();
            if (typeof loadTabsList === 'function') {
                loadTabsList();
            }

            sendResponse({ received: true });
        }
    });
}

// 全局变量
let selectedTabIds = new Set();
let allTabs = [];
let filteredTabs = [];
let searchQuery = '';

// 加载标签页列表
async function loadTabsList() {
    try {
        if (!chrome || !chrome.tabs) {
            return;
        }

        const tabs = await chrome.tabs.query({ currentWindow: true });

        const workspaceTabs = tabs.filter(tab => {
            const url = tab.url || '';
            return !url.includes('workspace-placeholder.html');
        });

        // 更新调试信息
        const debugText = document.getElementById('debugText');
        if (debugText) {
            const currentWindow = await chrome.windows.getCurrent();
            debugText.textContent = `窗口ID: ${currentWindow.id}, 原始标签页: ${tabs.length}个, 过滤后: ${workspaceTabs.length}个, 时间: ${new Date().toLocaleTimeString()}`;
        }

        allTabs = workspaceTabs;
        applySearchFilter();

        // 始终显示标签页列表区域，即使没有标签页也显示空状态
        const tabsSection = document.getElementById('tabsSection');
        if (tabsSection) {
            tabsSection.style.display = 'block';
        }

        // 更新统计信息
        updateTabsStats();
    } catch (error) {
        console.error('加载标签页列表时出错:', error);
    }
}

// 应用搜索过滤
function applySearchFilter() {
    if (!searchQuery.trim()) {
        filteredTabs = [...allTabs];
    } else {
        const query = searchQuery.toLowerCase();
        filteredTabs = allTabs.filter(tab => {
            const title = (tab.title || '').toLowerCase();
            const url = (tab.url || '').toLowerCase();
            return title.includes(query) || url.includes(query);
        });
    }

    renderTabsList(filteredTabs);
    updateTabsStats();
}

// 更新标签页统计信息
function updateTabsStats() {
    const totalCount = document.getElementById('totalTabsCount');
    const filteredCount = document.getElementById('filteredTabsCount');
    const headerCount = document.getElementById('headerTabsCount');

    if (totalCount) {
        totalCount.textContent = allTabs.length.toString();
    }

    if (filteredCount) {
        filteredCount.textContent = filteredTabs.length.toString();
    }

    // 更新标题中的计数
    if (headerCount) {
        headerCount.textContent = allTabs.length.toString();
    }
}

// 渲染标签页列表
function renderTabsList(tabs) {
    const tabsList = document.getElementById('tabsList');
    if (!tabsList) return;

    if (tabs.length === 0) {
        const emptyMessage = searchQuery.trim() ?
            `<div class="empty-tabs">未找到匹配 "${searchQuery}" 的标签页</div>` :
            '<div class="empty-tabs">暂无存储的标签页</div>';
        tabsList.innerHTML = emptyMessage;
        return;
    }

    tabsList.innerHTML = tabs.map(tab => {
        const title = escapeHtml(tab.title || '无标题');
        const url = escapeHtml(tab.url || '');
        const favicon = tab.favIconUrl || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>';

        // 高亮搜索关键词
        const highlightedTitle = highlightSearchTerm(title, searchQuery);
        const highlightedUrl = highlightSearchTerm(url, searchQuery);

        return `
            <div class="tab-item ${selectedTabIds.has(tab.id) ? 'selected' : ''}" data-tab-id="${tab.id}">
                <input type="checkbox" class="tab-checkbox" ${selectedTabIds.has(tab.id) ? 'checked' : ''}>
                <img class="tab-favicon" src="${favicon}" alt="favicon">
                <div class="tab-info">
                    <div class="tab-title">${highlightedTitle}</div>
                    <div class="tab-url">${highlightedUrl}</div>
                </div>
                <div class="tab-status">
                    <span class="status-badge status-active">活跃</span>
                    ${tab.pinned ? '<span class="status-badge status-pinned">已固定</span>' : ''}
                </div>
                <div class="tab-actions">
                    <button class="btn-small btn-danger delete-tab-btn" data-tab-id="${tab.id}" title="删除标签页">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 高亮搜索关键词
function highlightSearchTerm(text, query) {
    if (!query.trim()) return text;

    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
    return text.replace(regex, '<mark class="search-highlight">$1</mark>');
}

// 转义正则表达式特殊字符
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 切换标签页选择状态
async function toggleTabSelection(tabId) {
    // 验证标签页是否有效且不是占位符页面
    try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.url?.includes('workspace-placeholder.html')) {
            showErrorMessage('不能选择工作区占位符页面');
            return;
        }
    } catch (error) {
        // 如果标签页无效，从选择中移除并刷新列表
        selectedTabIds.delete(tabId);
        await loadTabsList();
        updateActionButtons();
        return;
    }

    if (selectedTabIds.has(tabId)) {
        selectedTabIds.delete(tabId);
    } else {
        selectedTabIds.add(tabId);
    }

    // 更新UI
    const tabItem = document.querySelector(`[data-tab-id="${tabId}"]`);
    if (tabItem) {
        tabItem.classList.toggle('selected', selectedTabIds.has(tabId));

        // 更新复选框状态
        const checkbox = tabItem.querySelector('.tab-checkbox');
        if (checkbox) {
            checkbox.checked = selectedTabIds.has(tabId);
        }
    }

    updateActionButtons();
}

// 更新操作按钮状态
function updateActionButtons() {
    const hasSelection = selectedTabIds.size > 0;
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    if (deleteBtn) deleteBtn.disabled = !hasSelection;

    // 更新全选按钮文本
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectAllBtn) {
        if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
            selectAllBtn.textContent = '取消全选';
        } else {
            selectAllBtn.textContent = '全选';
        }
    }
}

// 全选/取消全选
async function toggleSelectAll() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (!selectAllBtn) return;

    if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
        // 取消全选
        selectedTabIds.clear();
    } else {
        // 全选当前过滤的标签页，但要验证每个标签页
        selectedTabIds.clear();

        for (const tab of filteredTabs) {
            try {
                // 验证标签页是否仍然存在且不是占位符页面
                const currentTab = await chrome.tabs.get(tab.id);
                if (currentTab && !currentTab.url?.includes('workspace-placeholder.html')) {
                    selectedTabIds.add(tab.id);
                }
            } catch (error) {
                // 跳过无效标签页
            }
        }
    }

    renderTabsList(filteredTabs);
    updateActionButtons();
}

// 搜索功能
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchQuery = searchInput.value;
        applySearchFilter();
    }
}

// 清除搜索
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
        searchQuery = '';
        applySearchFilter();
    }
}

// 单个标签页操作

async function deleteTab(tabId) {
    try {
        // 先验证标签页是否存在
        try {
            await chrome.tabs.get(tabId);
        } catch (error) {
            await loadTabsList(); // 刷新列表以移除无效标签页
            return;
        }

        await chrome.tabs.remove(tabId);
        await loadTabsList();
    } catch (error) {
        // 显示错误提示
        showErrorMessage(`删除标签页失败: ${error.message}`);

        // 刷新列表以确保状态同步
        await loadTabsList();
    }
}



// 显示删除确认对话框
async function showDeleteConfirmation() {
    if (selectedTabIds.size === 0) return;

    // 验证选中的标签页是否仍然有效
    const validTabIds = [];
    const invalidTabIds = [];

    for (const tabId of selectedTabIds) {
        try {
            const tab = await chrome.tabs.get(tabId);
            if (tab && !tab.url?.includes('workspace-placeholder.html')) {
                validTabIds.push(tabId);
            } else {
                invalidTabIds.push(tabId);
            }
        } catch (error) {
            invalidTabIds.push(tabId);
        }
    }

    // 移除无效的标签页ID
    if (invalidTabIds.length > 0) {
        invalidTabIds.forEach(id => selectedTabIds.delete(id));

        // 更新UI
        await loadTabsList();
        updateActionButtons();
    }

    // 如果没有有效的标签页可删除
    if (validTabIds.length === 0) {
        showErrorMessage('没有可删除的标签页');
        return;
    }

    const modal = document.getElementById('deleteModal');
    const message = document.getElementById('deleteMessage');

    if (modal && message) {
        message.textContent = `确定要删除选中的 ${validTabIds.length} 个标签页吗？此操作无法撤销。`;
        modal.style.display = 'flex';
    }
}

// 隐藏删除确认对话框
function hideDeleteConfirmation() {
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.style.display = 'none';

        // 重置错误信息
        const message = document.getElementById('deleteMessage');
        if (message) {
            message.style.color = '';
        }
    }
}

// 显示错误提示
function showErrorMessage(message, duration = 5000) {

    // 创建错误提示元素
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 400px;
        font-size: 14px;
        line-height: 1.4;
    `;
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    // 自动移除
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, duration);
}

// 删除选中的标签页
async function deleteSelectedTabs() {
    if (selectedTabIds.size === 0) return;

    try {
        const tabIds = Array.from(selectedTabIds);

        // 验证标签页是否仍然存在
        const validTabIds = [];
        for (const tabId of tabIds) {
            try {
                const tab = await chrome.tabs.get(tabId);
                if (tab) {
                    validTabIds.push(tabId);
                }
            } catch (error) {
                // 标签页无效或已关闭
            }
        }

        if (validTabIds.length === 0) {
            selectedTabIds.clear();
            await loadTabsList();
            updateActionButtons();
            hideDeleteConfirmation();
            return;
        }

        await chrome.tabs.remove(validTabIds);

        // 清除选择并刷新列表
        selectedTabIds.clear();
        await loadTabsList();
        updateActionButtons();
        hideDeleteConfirmation();
    } catch (error) {
        // 显示用户友好的错误信息
        showErrorMessage(`批量删除失败: ${error.message}`);

        // 即使出错也要清理状态
        selectedTabIds.clear();
        await loadTabsList();
        updateActionButtons();
        hideDeleteConfirmation();
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    // 等待DOM完全加载
    if (document.readyState !== 'complete') {
        setTimeout(initializeEventListeners, 100);
        return;
    }

    // 搜索输入框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                clearSearch();
            }
        });
    }

    // 清除搜索按钮
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', clearSearch);
    }

    // 刷新按钮
    const refreshTabsBtn = document.getElementById('refreshTabsBtn');
    if (refreshTabsBtn) {
        refreshTabsBtn.addEventListener('click', async () => {
            await loadTabsList();
        });
    }

    // 标签页列表事件委托
    const tabsList = document.getElementById('tabsList');
    if (tabsList) {
        tabsList.addEventListener('click', (e) => {
            // 处理删除按钮点击
            if (e.target.classList.contains('delete-tab-btn')) {
                const tabId = parseInt(e.target.dataset.tabId);
                if (tabId) {
                    deleteTab(tabId);
                }
            }
        });

        tabsList.addEventListener('change', (e) => {
            // 处理复选框变化
            if (e.target.classList.contains('tab-checkbox')) {
                const tabItem = e.target.closest('.tab-item');
                if (tabItem) {
                    const tabId = parseInt(tabItem.dataset.tabId);
                    if (tabId) {
                        toggleTabSelection(tabId);
                    }
                }
            }
        });
    }

    // 全选按钮
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', toggleSelectAll);
    }



    // 删除选中按钮
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', showDeleteConfirmation);
    }

    // 确认删除按钮
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', deleteSelectedTabs);
    }

    // 取消删除按钮
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', hideDeleteConfirmation);
    }

    // 点击模态框背景关闭
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideDeleteConfirmation();
            }
        });
    }
}

// 检查Chrome扩展权限和API可用性
async function checkPermissions() {
    if (!chrome) {
        return false;
    }

    if (!chrome.tabs) {
        return false;
    }

    try {
        // 尝试查询标签页以测试权限
        await chrome.tabs.query({ currentWindow: true });
        return true;
    } catch (error) {
        return false;
    }
}

// 工作区窗口单例管理
class WorkspaceWindowManager {
    constructor() {
        this.workspaceWindowId = null;
        this.isCreatingWindow = false;
    }

    // 检查窗口是否存在
    async isWindowExists(windowId) {
        if (!windowId) return false;

        try {
            await chrome.windows.get(windowId);
            return true;
        } catch (error) {
            return false;
        }
    }

    // 获取或创建工作区窗口
    async getOrCreateWorkspaceWindow() {
        // 防止并发创建多个窗口
        if (this.isCreatingWindow) {
            // 等待当前创建操作完成
            while (this.isCreatingWindow) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return this.workspaceWindowId;
        }

        // 检查现有窗口是否仍然存在
        if (this.workspaceWindowId && await this.isWindowExists(this.workspaceWindowId)) {
            return this.workspaceWindowId;
        }

        // 创建新的工作区窗口
        this.isCreatingWindow = true;
        try {
            const window = await chrome.windows.create({
                url: chrome.runtime.getURL('workspace-placeholder.html'),
                type: 'normal',
                state: 'maximized'
            });

            this.workspaceWindowId = window.id;

            // 监听窗口关闭事件
            chrome.windows.onRemoved.addListener((windowId) => {
                if (windowId === this.workspaceWindowId) {
                    this.workspaceWindowId = null;
                }
            });

            return this.workspaceWindowId;
        } catch (error) {
            this.workspaceWindowId = null;
            throw error;
        } finally {
            this.isCreatingWindow = false;
        }
    }

    // 将标签页移动到工作区窗口
    async moveTabsToWorkspace(tabIds) {
        try {
            const windowId = await this.getOrCreateWorkspaceWindow();
            if (!windowId) {
                throw new Error('无法获取工作区窗口');
            }

            // 移动标签页到工作区窗口
            await chrome.tabs.move(tabIds, { windowId, index: -1 });

            // 激活工作区窗口
            await chrome.windows.update(windowId, { focused: true });

            return windowId;
        } catch (error) {
            throw error;
        }
    }
}

// 创建全局工作区窗口管理器实例
const workspaceWindowManager = new WorkspaceWindowManager();

// 初始化所有功能
async function initializeAll() {
    // 检查权限
    const hasPermissions = await checkPermissions();
    if (!hasPermissions) {
        // 简化权限错误处理 - 显示简洁的错误信息
        const tabsSection = document.getElementById('tabsSection');
        if (tabsSection) {
            tabsSection.innerHTML = `
                <div class="error-message">
                    <h3>⚠️ 权限不足</h3>
                    <p>请重新加载扩展或检查权限设置</p>
                </div>
            `;
            tabsSection.style.display = 'block';
        }
        return;
    }

    initializeEventListeners();
    await updateTabCount();
    await loadTabsList();

    // 设置定期更新
    setInterval(async () => {
        await updateTabCount();
        await loadTabsList();
    }, 3000); // 每3秒更新一次
}

// 移除了全局函数以符合CSP要求

// 确保DOM完全加载后再初始化
function ensureInitialization() {
    if (document.readyState === 'complete') {
        initializeAll();
    } else {
        setTimeout(ensureInitialization, 50);
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(ensureInitialization, 100);
    });
} else {
    // DOM已经加载完成，立即初始化
    ensureInitialization();
}
