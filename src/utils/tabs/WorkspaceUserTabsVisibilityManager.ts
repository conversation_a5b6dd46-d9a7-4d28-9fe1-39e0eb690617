import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { WorkonaTabManager } from '../workonaTabManager';

/**
 * 工作区级别的用户标签页可见性管理器（重构版：按照旧代码逻辑重新实现）
 * 职责：管理特定工作区中的用户标签页隐藏和显示
 */
export class WorkspaceUserTabsVisibilityManager {

  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    pinnedTabIds: number[];
    totalUserTabs: number;
    visibleUserTabs: number;
    canContinueHiding: boolean;
    actionType: 'hide' | 'continue_hide' | 'show';
  }>> {
    try {
      // 检查全局用户标签模式
      const { StorageManager } = await import('../storage');
      const settingsResult = await StorageManager.getSettings();
      if (settingsResult.success && settingsResult.data!.globalUserTabMode) {
        // 在全局模式下，检查全局隐藏状态
        const globalStateResult = await chrome.storage.local.get(['globalUserTabsHidden', 'globalHiddenTabIds']);
        const isGloballyHidden = globalStateResult.globalUserTabsHidden || false;
        const globalHiddenTabIds = globalStateResult.globalHiddenTabIds || [];

        // 统计当前窗口的用户标签页
        const currentWindow = await chrome.windows.getCurrent();
        const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
        let totalUserTabs = 0;
        let visibleUserTabs = 0;

        for (const tab of allTabs) {
          if (!tab.url || !tab.id) continue;
          const { TabClassificationUtils } = await import('../tabs');
          const isUserTab = TabClassificationUtils.isUserTab(tab.url);
          if (isUserTab) {
            totalUserTabs++;
            if (!globalHiddenTabIds.includes(tab.id)) {
              visibleUserTabs++;
            }
          }
        }

        // 确定操作类型
        let actionType: 'hide' | 'continue_hide' | 'show';
        const canContinueHiding = isGloballyHidden && visibleUserTabs > 0;

        if (isGloballyHidden && globalHiddenTabIds.length > 0) {
          actionType = canContinueHiding ? 'continue_hide' : 'show';
        } else {
          actionType = 'hide';
        }

        return {
          success: true,
          data: {
            isHidden: isGloballyHidden,
            hiddenTabIds: globalHiddenTabIds,
            pinnedTabIds: [],
            totalUserTabs,
            visibleUserTabs,
            canContinueHiding,
            actionType
          }
        };
      }

      // 从存储中获取工作区隐藏状态
      const result = await chrome.storage.local.get([
        `workspaceUserTabsHidden_${workspaceId}`,
        `workspaceHiddenTabIds_${workspaceId}`,
        `workspacePinnedTabIds_${workspaceId}`
      ]);

      const isHidden = result[`workspaceUserTabsHidden_${workspaceId}`] || false;
      const hiddenTabIds = result[`workspaceHiddenTabIds_${workspaceId}`] || [];
      const pinnedTabIds = result[`workspacePinnedTabIds_${workspaceId}`] || [];

      // 获取当前窗口的所有标签页
      const { TabManager } = await import('../tabs');
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return { success: false, error: allTabsResult.error };
      }

      const allTabs = allTabsResult.data!;
      const currentWindow = await chrome.windows.getCurrent();
      
      let totalUserTabs = 0;
      let visibleUserTabs = 0;

      // 统计用户标签页数量
      for (const tab of allTabs) {
        if (tab.windowId !== currentWindow.id) {
          continue;
        }

        // 使用精确的用户标签页判断逻辑（基于旧实现的UserTabsUtils.isRealUserTab）
        const { UserTabsUtils } = await import('../tabs');

        // 排除系统页面
        if (UserTabsUtils.isSystemTab(tab.url) || UserTabsUtils.isExtensionTab(tab.url)) {
          continue;
        }

        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id!);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有Workona ID映射的非系统标签页是用户标签页
          // 继续处理
        } else {
          // 检查是否为工作区核心标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data) {
            const { isWorkspaceCore } = metadataResult.data;
            if (isWorkspaceCore) {
              // 工作区核心标签页不是用户标签页，跳过
              continue;
            }
            // 非工作区核心标签页（会话临时标签页）是用户标签页，继续处理
          } else {
            // 映射损坏的标签页也算作用户标签页，继续处理
          }
        }

        // 检查是否属于当前工作区（复用之前的workonaIdResult）
        if (workonaIdResult && workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split('-')[1];
          if (tabWorkspaceId === workspaceId) {
            totalUserTabs++;
            if (!hiddenTabIds.includes(tab.id!)) {
              visibleUserTabs++;
            }
          }
        } else {
          // 对于没有 Workona ID 的用户标签页，也计入统计
          totalUserTabs++;
          if (!hiddenTabIds.includes(tab.id!)) {
            visibleUserTabs++;
          }
        }
      }

      // 确定操作类型
      let actionType: 'hide' | 'continue_hide' | 'show';
      const canContinueHiding = isHidden && visibleUserTabs > 0;

      if (isHidden && hiddenTabIds.length > 0) {
        actionType = canContinueHiding ? 'continue_hide' : 'show';
      } else {
        actionType = 'hide';
      }

      return {
        success: true,
        data: {
          isHidden,
          hiddenTabIds,
          pinnedTabIds,
          totalUserTabs,
          visibleUserTabs,
          canContinueHiding,
          actionType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 切换工作区用户标签页可见性（兼容原版本签名）
   */
  static async toggleWorkspaceUserTabsVisibility(workspaceOrId: string | import('@/types/workspace').WorkSpace): Promise<OperationResult<{
    action: 'hidden' | 'shown' | 'continue_hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
    tabIds?: number[]; // 兼容原版本
  }>> {
    try {
      // 兼容原版本：支持传入WorkSpace对象或workspaceId字符串
      const workspaceId = typeof workspaceOrId === 'string' ? workspaceOrId : workspaceOrId.id;

      // 检查全局用户标签模式
      const { StorageManager } = await import('../storage');
      const settingsResult = await StorageManager.getSettings();
      const isGlobalMode = settingsResult.success && settingsResult.data!.globalUserTabMode;

      if (isGlobalMode) {
        console.log(`🌐 全局用户标签模式已启用，执行全局用户标签页隐藏/显示操作`);
        return await this.toggleGlobalUserTabsVisibility();
      }

      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }

      const state = stateResult.data!;

      // 根据当前状态决定操作
      if (state.actionType === 'show') {
        // 显示所有隐藏的用户标签页
        return await this.showWorkspaceUserTabs(workspaceId);
      } else if (state.actionType === 'continue_hide') {
        // 继续隐藏剩余的用户标签页
        return await this.continueHideWorkspaceUserTabs(workspaceId);
      } else {
        // 隐藏用户标签页
        return await this.hideWorkspaceUserTabs(workspaceId);
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle workspace user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 隐藏工作区用户标签页（重构：按照旧代码逻辑重新实现）
   */
  static async hideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<{
    action: 'hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      console.log(`🔒 开始隐藏工作区 ${workspaceId} 的用户标签页`);

      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }

      const currentState = stateResult.data!;

      // 获取所有标签页
      const { TabManager } = await import('../tabs');
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return { success: false, error: allTabsResult.error };
      }

      const allTabs = allTabsResult.data!;
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中属于该工作区的用户标签页
      const workspaceUserTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId !== currentWindow.id) {
          continue;
        }

        // 使用精确的用户标签页判断逻辑（基于旧实现的UserTabsUtils.isRealUserTab）
        const { UserTabsUtils } = await import('../tabs');

        // 排除系统页面
        if (UserTabsUtils.isSystemTab(tab.url) || UserTabsUtils.isExtensionTab(tab.url)) {
          continue;
        }

        let isRealUserTab = false;

        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id!);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有Workona ID映射的非系统标签页是用户标签页
          isRealUserTab = true;
        } else {
          // 检查是否为工作区核心标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data) {
            const { isWorkspaceCore } = metadataResult.data;
            if (!isWorkspaceCore) {
              // 非工作区核心标签页（会话临时标签页）是用户标签页
              isRealUserTab = true;
            }
          } else {
            // 映射损坏的标签页也算作用户标签页
            isRealUserTab = true;
          }
        }

        if (isRealUserTab && !currentState.hiddenTabIds.includes(tab.id!)) {
          workspaceUserTabs.push(tab);
          console.log(`✅ 将用户标签页包含在隐藏操作中: ${tab.title} (${tab.url})`);
        }
      }

      if (workspaceUserTabs.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有用户标签页需要隐藏`);
        return { 
          success: true, 
          data: { 
            action: 'hidden', 
            affectedTabsCount: 0, 
            totalUserTabs: currentState.totalUserTabs 
          } 
        };
      }

      console.log(`📤 准备隐藏工作区 ${workspaceId} 的 ${workspaceUserTabs.length} 个用户标签页`);

      // 记录哪些标签页是固定的（基于 Workona ID 映射的元数据）
      const pinnedTabIds: number[] = [];
      for (const tab of workspaceUserTabs) {
        // 检查标签页的实际固定状态和元数据中的固定状态
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id!);
        let shouldRecordAsPinned = tab.isPinned;

        if (workonaIdResult.success && workonaIdResult.data) {
          // 如果有 Workona ID，检查元数据中的固定状态
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== undefined) {
            // 使用元数据中的固定状态作为权威状态
            shouldRecordAsPinned = metadataResult.data.metadata.isPinned;
          }
        }

        if (shouldRecordAsPinned) {
          pinnedTabIds.push(tab.id!);
          console.log(`📌 记录固定标签页: ${tab.title} (${tab.id}) - 基于${workonaIdResult.success ? 'Workona元数据' : '实际状态'}`);
        }
      }

      // 移动用户标签页到工作区专用窗口
      const tabIds = workspaceUserTabs.map(tab => tab.id!).filter(id => id && typeof id === 'number' && id > 0);

      if (tabIds.length === 0) {
        console.warn(`⚠️ 工作区 ${workspaceId} 没有有效的标签页ID可以隐藏`);
        return { 
          success: true, 
          data: { 
            action: 'hidden', 
            affectedTabsCount: 0, 
            totalUserTabs: currentState.totalUserTabs 
          } 
        };
      }

      // 使用工作区专用窗口管理器移动标签页
      const { WindowManager } = await import('../windowManager');
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspaceId,
        `工作区 ${workspaceId} - 隐藏的用户标签页`
      );

      if (!moveResult.success) {
        console.error(`❌ 移动工作区 ${workspaceId} 用户标签页到隐藏窗口失败:`, moveResult.error);
        return { success: false, error: moveResult.error };
      }

      // 更新工作区隐藏状态，包括固定状态
      const newHiddenTabIds = [...currentState.hiddenTabIds, ...tabIds];
      const existingPinnedTabIds = currentState.pinnedTabIds || [];
      const allPinnedTabIds = [...existingPinnedTabIds, ...pinnedTabIds];
      
      // 保存状态到存储
      await chrome.storage.local.set({
        [`workspaceUserTabsHidden_${workspaceId}`]: true,
        [`workspaceHiddenTabIds_${workspaceId}`]: newHiddenTabIds,
        [`workspacePinnedTabIds_${workspaceId}`]: allPinnedTabIds
      });

      console.log(`✅ 成功隐藏工作区 ${workspaceId} 的 ${tabIds.length} 个用户标签页`);
      
      return {
        success: true,
        data: {
          action: 'hidden',
          affectedTabsCount: tabIds.length,
          totalUserTabs: currentState.totalUserTabs
        }
      };
    } catch (error) {
      console.error(`❌ 隐藏工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示工作区用户标签页（重构：按照旧代码逻辑重新实现）
   */
  static async showWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<{
    action: 'shown';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      console.log(`🔓 开始显示工作区 ${workspaceId} 的隐藏用户标签页`);

      // 获取隐藏状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }

      const { isHidden, hiddenTabIds, pinnedTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有隐藏的用户标签页需要显示`);
        return { 
          success: true, 
          data: { 
            action: 'shown', 
            affectedTabsCount: 0, 
            totalUserTabs: stateResult.data!.totalUserTabs 
          } 
        };
      }

      console.log(`📥 准备显示工作区 ${workspaceId} 的 ${hiddenTabIds.length} 个隐藏用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 工作区 ${workspaceId} 的标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 所有隐藏的标签页都已不存在`);
        await chrome.storage.local.set({
          [`workspaceUserTabsHidden_${workspaceId}`]: false,
          [`workspaceHiddenTabIds_${workspaceId}`]: [],
          [`workspacePinnedTabIds_${workspaceId}`]: []
        });
        return { 
          success: true, 
          data: { 
            action: 'shown', 
            affectedTabsCount: 0, 
            totalUserTabs: stateResult.data!.totalUserTabs 
          } 
        };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 直接使用 Chrome API 移动标签页回到主窗口
      try {
        console.log(`📥 移动 ${existingTabIds.length} 个隐藏标签页回到主窗口 ${currentWindow.id}`);

        await chrome.tabs.move(existingTabIds, {
          windowId: currentWindow.id!,
          index: -1 // 移动到窗口末尾
        });

        // 恢复固定状态（基于 Workona ID 映射的元数据）
        for (const tabId of existingTabIds) {
          try {
            // 检查标签页的 Workona ID 和元数据
            const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
            let shouldRestorePinned = pinnedTabIds.includes(tabId);

            if (workonaIdResult.success && workonaIdResult.data) {
              // 如果有 Workona ID，检查元数据中的固定状态
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
              if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== undefined) {
                // 使用元数据中的固定状态作为权威状态
                shouldRestorePinned = metadataResult.data.metadata.isPinned;
              }
            }

            if (shouldRestorePinned) {
              await chrome.tabs.update(tabId, { pinned: true });
              console.log(`📌 恢复固定状态: 标签页 ${tabId} - 基于${workonaIdResult.success ? 'Workona元数据' : '存储记录'}`);
            } else {
              // 确保非固定标签页不会被错误地设置为固定
              const currentTab = await chrome.tabs.get(tabId);
              if (currentTab.pinned) {
                await chrome.tabs.update(tabId, { pinned: false });
                console.log(`📌 取消错误的固定状态: 标签页 ${tabId}`);
              }
            }
          } catch (error) {
            console.warn(`⚠️ 处理标签页 ${tabId} 固定状态失败:`, error);
          }
        }

        // 清除隐藏状态
        await chrome.storage.local.set({
          [`workspaceUserTabsHidden_${workspaceId}`]: false,
          [`workspaceHiddenTabIds_${workspaceId}`]: [],
          [`workspacePinnedTabIds_${workspaceId}`]: []
        });

        console.log(`✅ 成功显示工作区 ${workspaceId} 的 ${existingTabIds.length} 个隐藏用户标签页`);
        
        return {
          success: true,
          data: {
            action: 'shown',
            affectedTabsCount: existingTabIds.length,
            totalUserTabs: stateResult.data!.totalUserTabs
          }
        };
      } catch (moveError) {
        console.error(`❌ 移动工作区 ${workspaceId} 隐藏标签页回主窗口失败:`, moveError);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Failed to move tabs back to main window',
            details: moveError,
          },
        };
      }
    } catch (error) {
      console.error(`❌ 显示工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 继续隐藏工作区用户标签页
   */
  static async continueHideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<{
    action: 'continue_hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      // 检查全局用户标签模式
      const { StorageManager } = await import('../storage');
      const settingsResult = await StorageManager.getSettings();
      const isGlobalMode = settingsResult.success && settingsResult.data!.globalUserTabMode;

      if (isGlobalMode) {
        console.log(`🌐 全局用户标签模式已启用，执行全局继续隐藏操作`);
        return await this.continueHideGlobalUserTabs();
      }

      // 复用隐藏逻辑
      const hideResult = await this.hideWorkspaceUserTabs(workspaceId);
      if (!hideResult.success) {
        return { success: false, error: hideResult.error };
      }

      return {
        success: true,
        data: {
          action: 'continue_hidden',
          affectedTabsCount: hideResult.data?.affectedTabsCount || 0,
          totalUserTabs: hideResult.data?.totalUserTabs || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to continue hide workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 全局用户标签页隐藏/显示切换（全局模式专用）
   */
  static async toggleGlobalUserTabsVisibility(): Promise<OperationResult<{
    action: 'hidden' | 'shown' | 'continue_hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
    tabIds?: number[];
  }>> {
    try {
      console.log(`🌐 执行全局用户标签页隐藏/显示切换`);

      // 获取全局状态（复用getWorkspaceUserTabsState的逻辑）
      const stateResult = await this.getWorkspaceUserTabsState('global'); // 使用'global'作为特殊标识
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }

      const state = stateResult.data!;

      // 根据当前状态决定操作
      if (state.actionType === 'show') {
        // 显示所有全局隐藏的用户标签页
        return await this.showGlobalUserTabs();
      } else if (state.actionType === 'continue_hide') {
        // 继续隐藏新的用户标签页
        return await this.continueHideGlobalUserTabs();
      } else {
        // 隐藏所有用户标签页
        return await this.hideGlobalUserTabs();
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle global user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 隐藏所有用户标签页（全局模式）
   */
  static async hideGlobalUserTabs(): Promise<OperationResult<{
    action: 'hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
    tabIds?: number[];
  }>> {
    try {
      console.log(`🌐 开始隐藏所有用户标签页（全局模式）`);

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 筛选出用户标签页
      const userTabs: chrome.tabs.Tab[] = [];
      for (const tab of allTabs) {
        if (!tab.url || !tab.id) {
          continue;
        }

        // 使用精确的用户标签页判断逻辑（基于旧实现的UserTabsUtils.isRealUserTab）
        const { UserTabsUtils } = await import('../tabs');

        // 排除系统页面
        if (UserTabsUtils.isSystemTab(tab.url) || UserTabsUtils.isExtensionTab(tab.url)) {
          continue;
        }

        let isRealUserTab = false;

        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有Workona ID映射的非系统标签页是用户标签页
          isRealUserTab = true;
        } else {
          // 检查是否为工作区核心标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data) {
            const { isWorkspaceCore } = metadataResult.data;
            if (!isWorkspaceCore) {
              // 非工作区核心标签页（会话临时标签页）是用户标签页
              isRealUserTab = true;
            }
          } else {
            // 映射损坏的标签页也算作用户标签页
            isRealUserTab = true;
          }
        }

        if (isRealUserTab) {
          userTabs.push(tab);
          console.log(`✅ 将用户标签页包含在全局隐藏操作中: ${tab.title} (${tab.url})`);
        }
      }

      if (userTabs.length === 0) {
        console.log(`⚠️ 没有用户标签页需要隐藏`);
        return {
          success: true,
          data: {
            action: 'hidden',
            affectedTabsCount: 0,
            totalUserTabs: 0
          }
        };
      }

      // 移动用户标签页到全局专用窗口
      const tabIds = userTabs.map(tab => tab.id!);
      const { WindowManager } = await import('../windowManager');
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        'global',
        '全局隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error(`❌ 移动全局用户标签页到隐藏窗口失败:`, moveResult.error);
        return { success: false, error: moveResult.error };
      }

      // 保存全局隐藏状态
      await chrome.storage.local.set({
        globalUserTabsHidden: true,
        globalHiddenTabIds: tabIds
      });

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页（全局模式）`);
      return {
        success: true,
        data: {
          action: 'hidden',
          affectedTabsCount: tabIds.length,
          totalUserTabs: userTabs.length,
          tabIds
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide global user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示所有用户标签页（全局模式）
   */
  static async showGlobalUserTabs(): Promise<OperationResult<{
    action: 'shown';
    affectedTabsCount: number;
    totalUserTabs: number;
    tabIds?: number[];
  }>> {
    try {
      console.log(`🌐 开始显示所有隐藏的用户标签页（全局模式）`);

      // 获取全局隐藏状态
      const globalStateResult = await chrome.storage.local.get(['globalHiddenTabIds']);
      const globalHiddenTabIds = globalStateResult.globalHiddenTabIds || [];

      if (globalHiddenTabIds.length === 0) {
        console.log(`⚠️ 没有全局隐藏的用户标签页需要显示`);
        return {
          success: true,
          data: {
            action: 'shown',
            affectedTabsCount: 0,
            totalUserTabs: 0
          }
        };
      }

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of globalHiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 全局隐藏的标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log(`⚠️ 所有全局隐藏的标签页都已不存在`);
        await chrome.storage.local.set({
          globalUserTabsHidden: false,
          globalHiddenTabIds: []
        });
        return {
          success: true,
          data: {
            action: 'shown',
            affectedTabsCount: 0,
            totalUserTabs: 0
          }
        };
      }

      // 移动标签页回主窗口
      const currentWindow = await chrome.windows.getCurrent();
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1
      });

      // 清除全局隐藏状态
      await chrome.storage.local.set({
        globalUserTabsHidden: false,
        globalHiddenTabIds: []
      });

      console.log(`✅ 成功显示 ${existingTabIds.length} 个全局隐藏的用户标签页`);
      return {
        success: true,
        data: {
          action: 'shown',
          affectedTabsCount: existingTabIds.length,
          totalUserTabs: existingTabIds.length,
          tabIds: existingTabIds
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show global user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 继续隐藏新的用户标签页（全局模式）
   */
  static async continueHideGlobalUserTabs(): Promise<OperationResult<{
    action: 'continue_hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
    tabIds?: number[];
  }>> {
    try {
      console.log(`🌐 开始继续隐藏新的用户标签页（全局模式）`);

      // 获取全局隐藏状态
      const globalStateResult = await chrome.storage.local.get(['globalHiddenTabIds']);
      const globalHiddenTabIds = globalStateResult.globalHiddenTabIds || [];

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 筛选出新的用户标签页（不在隐藏列表中的）
      const newUserTabs: chrome.tabs.Tab[] = [];
      for (const tab of allTabs) {
        if (!tab.url || !tab.id) {
          continue;
        }

        // 使用精确的用户标签页判断逻辑（基于旧实现的UserTabsUtils.isRealUserTab）
        const { UserTabsUtils } = await import('../tabs');

        // 排除系统页面
        if (UserTabsUtils.isSystemTab(tab.url) || UserTabsUtils.isExtensionTab(tab.url)) {
          continue;
        }

        let isRealUserTab = false;

        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有Workona ID映射的非系统标签页是用户标签页
          isRealUserTab = true;
        } else {
          // 检查是否为工作区核心标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data) {
            const { isWorkspaceCore } = metadataResult.data;
            if (!isWorkspaceCore) {
              // 非工作区核心标签页（会话临时标签页）是用户标签页
              isRealUserTab = true;
            }
          } else {
            // 映射损坏的标签页也算作用户标签页
            isRealUserTab = true;
          }
        }

        // 只有真正的用户标签页且不在隐藏列表中的才是需要继续隐藏的标签页
        if (isRealUserTab && !globalHiddenTabIds.includes(tab.id)) {
          newUserTabs.push(tab);
          console.log(`✅ 将新的用户标签页包含在继续隐藏操作中: ${tab.title} (${tab.url})`);
        }
      }

      if (newUserTabs.length === 0) {
        console.log(`⚠️ 没有新的用户标签页需要继续隐藏`);
        return {
          success: true,
          data: {
            action: 'continue_hidden',
            affectedTabsCount: 0,
            totalUserTabs: globalHiddenTabIds.length
          }
        };
      }

      // 移动新的用户标签页到全局专用窗口
      const newTabIds = newUserTabs.map(tab => tab.id!);
      const { WindowManager } = await import('../windowManager');
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        newTabIds,
        'global',
        '全局隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error(`❌ 移动新的全局用户标签页到隐藏窗口失败:`, moveResult.error);
        return { success: false, error: moveResult.error };
      }

      // 更新全局隐藏状态，添加新的标签页ID
      const updatedHiddenTabIds = [...globalHiddenTabIds, ...newTabIds];
      await chrome.storage.local.set({
        globalUserTabsHidden: true,
        globalHiddenTabIds: updatedHiddenTabIds
      });

      console.log(`✅ 成功继续隐藏 ${newTabIds.length} 个新的用户标签页（全局模式）`);
      return {
        success: true,
        data: {
          action: 'continue_hidden',
          affectedTabsCount: newTabIds.length,
          totalUserTabs: updatedHiddenTabIds.length,
          tabIds: newTabIds
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to continue hide global user tabs',
          details: error,
        },
      };
    }
  }
}
