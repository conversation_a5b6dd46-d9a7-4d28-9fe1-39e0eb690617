import React, { useState, useEffect } from 'react';
import { <PERSON>, Pi<PERSON>, <PERSON>nOff, <PERSON>, EyeOff, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/Toast';

interface UserTab {
  id: number;
  title: string;
  url: string;
  pinned: boolean;
  visible: boolean;
  workspaceId?: string;
  workspaceName?: string;
  favicon?: string;
}

interface UserTabsManagerProps {
  className?: string;
}

/**
 * 用户标签页管理组件
 * 职责：显示和管理所有用户标签页
 */
const UserTabsManager: React.FC<UserTabsManagerProps> = ({ className = '' }) => {
  const [userTabs, setUserTabs] = useState<UserTab[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { showError, showSuccess } = useToast();

  /**
   * 加载用户标签页数据
   */
  const loadUserTabs = async () => {
    try {
      setIsLoading(true);
      
      // 获取所有窗口的标签页
      const allWindows = await chrome.windows.getAll({ populate: true });
      const allTabs: chrome.tabs.Tab[] = [];
      
      for (const window of allWindows) {
        if (window.tabs) {
          allTabs.push(...window.tabs);
        }
      }
      
      // 筛选用户标签页
      const { TabClassificationUtils } = await import('@/utils/tabs');
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const { StorageManager } = await import('@/utils/storage');
      
      const userTabsData: UserTab[] = [];
      const workspacesResult = await StorageManager.getWorkspaces();
      const workspaces = workspacesResult.success ? workspacesResult.data! : [];
      
      for (const tab of allTabs) {
        if (!tab.url || !tab.id) continue;
        
        const isUserTab = TabClassificationUtils.isUserTab(tab.url);
        if (isUserTab) {
          // 获取工作区归属
          let workspaceId: string | undefined;
          let workspaceName: string | undefined;
          
          try {
            const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
            if (workonaIdResult.success && workonaIdResult.data) {
              workspaceId = workonaIdResult.data.split('-')[1];
              const workspace = workspaces.find(w => w.id === workspaceId);
              workspaceName = workspace?.name || `工作区 ${workspaceId}`;
            }
          } catch (error) {
            console.error(`获取标签页 ${tab.id} 工作区归属失败:`, error);
          }
          
          // 检查可见性（是否在主窗口）
          const currentWindow = await chrome.windows.getCurrent();
          const visible = tab.windowId === currentWindow.id;
          
          userTabsData.push({
            id: tab.id,
            title: tab.title || '无标题',
            url: tab.url,
            pinned: tab.pinned || false,
            visible,
            workspaceId,
            workspaceName: workspaceName || '未分配',
            favicon: tab.favIconUrl
          });
        }
      }
      
      setUserTabs(userTabsData);
    } catch (error) {
      console.error('加载用户标签页失败:', error);
      showError('加载用户标签页失败');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 关闭标签页
   */
  const closeTab = async (tabId: number) => {
    try {
      await chrome.tabs.remove(tabId);
      showSuccess('标签页已关闭');
      await loadUserTabs(); // 重新加载数据
    } catch (error) {
      console.error('关闭标签页失败:', error);
      showError('关闭标签页失败');
    }
  };

  /**
   * 切换标签页固定状态
   */
  const togglePin = async (tabId: number, currentPinned: boolean) => {
    try {
      await chrome.tabs.update(tabId, { pinned: !currentPinned });
      showSuccess(currentPinned ? '已取消固定' : '已固定标签页');
      await loadUserTabs(); // 重新加载数据
    } catch (error) {
      console.error('切换固定状态失败:', error);
      showError('切换固定状态失败');
    }
  };

  /**
   * 初始化加载
   */
  useEffect(() => {
    loadUserTabs();
  }, []);

  if (isLoading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-white">用户标签页管理</h3>
          <RefreshCw className="w-4 h-4 text-slate-400 animate-spin" />
        </div>
        <div className="text-xs text-slate-400">加载中...</div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-white">
          用户标签页管理 ({userTabs.length})
        </h3>
        <button
          onClick={loadUserTabs}
          className="p-1 hover:bg-slate-700 rounded transition-colors duration-200"
          title="刷新"
        >
          <RefreshCw className="w-4 h-4 text-slate-400" />
        </button>
      </div>

      {/* 标签页列表 */}
      {userTabs.length === 0 ? (
        <div className="text-xs text-slate-400 py-4 text-center">
          没有用户标签页
        </div>
      ) : (
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {userTabs.map((tab) => (
            <div
              key={tab.id}
              className="flex items-center gap-2 p-2 bg-slate-800/50 rounded border border-slate-700 hover:border-slate-600 transition-colors"
            >
              {/* 网站图标 */}
              {tab.favicon ? (
                <img
                  src={tab.favicon}
                  alt=""
                  className="w-4 h-4 flex-shrink-0"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-4 h-4 bg-slate-600 rounded flex-shrink-0" />
              )}

              {/* 标签页信息 */}
              <div className="flex-1 min-w-0">
                <div className="text-xs text-white truncate" title={tab.title}>
                  {tab.title}
                </div>
                <div className="text-xs text-slate-400 truncate" title={tab.url}>
                  {tab.url}
                </div>
                <div className="text-xs text-slate-500">
                  {tab.workspaceName} • {tab.visible ? '可见' : '隐藏'}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center gap-1">
                {/* 可见性指示器 */}
                <div className="p-1" title={tab.visible ? '在主窗口中可见' : '在后台窗口中'}>
                  {tab.visible ? (
                    <Eye className="w-3 h-3 text-green-400" />
                  ) : (
                    <EyeOff className="w-3 h-3 text-slate-500" />
                  )}
                </div>

                {/* 固定/取消固定按钮 */}
                <button
                  onClick={() => togglePin(tab.id, tab.pinned)}
                  className="p-1 hover:bg-slate-600 rounded transition-colors"
                  title={tab.pinned ? '取消固定' : '固定标签页'}
                >
                  {tab.pinned ? (
                    <PinOff className="w-3 h-3 text-orange-400" />
                  ) : (
                    <Pin className="w-3 h-3 text-slate-400" />
                  )}
                </button>

                {/* 关闭按钮 */}
                <button
                  onClick={() => closeTab(tab.id)}
                  className="p-1 hover:bg-slate-600 rounded transition-colors"
                  title="关闭标签页"
                >
                  <X className="w-3 h-3 text-red-400" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserTabsManager;
