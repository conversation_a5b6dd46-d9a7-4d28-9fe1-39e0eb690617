import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import UserTabsManager from '@/components/UserTabsManager';

interface CollapsibleUserTabsManagerProps {
  className?: string;
}

/**
 * 可展开/收起的用户标签页管理组件
 * 职责：提供可折叠的用户标签页管理界面
 */
const CollapsibleUserTabsManager: React.FC<CollapsibleUserTabsManagerProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [userTabsCount, setUserTabsCount] = useState<number>(0);

  /**
   * 加载展开状态
   */
  useEffect(() => {
    const loadExpandedState = async () => {
      try {
        const result = await chrome.storage.local.get(['userTabsManagerExpanded']);
        setIsExpanded(result.userTabsManagerExpanded || false);
      } catch (error) {
        console.error('加载用户标签页管理展开状态失败:', error);
      }
    };

    loadExpandedState();
  }, []);

  /**
   * 保存展开状态
   */
  const saveExpandedState = async (expanded: boolean) => {
    try {
      await chrome.storage.local.set({ userTabsManagerExpanded: expanded });
    } catch (error) {
      console.error('保存用户标签页管理展开状态失败:', error);
    }
  };

  /**
   * 切换展开/收起状态
   */
  const toggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    saveExpandedState(newExpanded);
  };

  /**
   * 获取用户标签页数量
   */
  const getUserTabsCount = async () => {
    try {
      // 获取所有窗口的标签页
      const allWindows = await chrome.windows.getAll({ populate: true });
      const allTabs: chrome.tabs.Tab[] = [];
      
      for (const window of allWindows) {
        if (window.tabs) {
          allTabs.push(...window.tabs);
        }
      }
      
      // 筛选用户标签页
      const { TabClassificationUtils } = await import('@/utils/tabs');
      let count = 0;
      
      for (const tab of allTabs) {
        if (tab.url && tab.id) {
          const isUserTab = TabClassificationUtils.isUserTab(tab.url);
          if (isUserTab) {
            count++;
          }
        }
      }
      
      setUserTabsCount(count);
    } catch (error) {
      console.error('获取用户标签页数量失败:', error);
      setUserTabsCount(0);
    }
  };

  /**
   * 初始化和定期更新标签页数量
   */
  useEffect(() => {
    getUserTabsCount();
    
    // 每5秒更新一次数量
    const interval = setInterval(getUserTabsCount, 5000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`bg-slate-800/50 rounded-lg border border-slate-700 ${className}`}>
      {/* 标题栏 */}
      <button
        onClick={toggleExpanded}
        className="w-full flex items-center justify-between p-4 hover:bg-slate-700/30 transition-colors duration-200 rounded-lg"
      >
        <div className="flex items-center gap-3">
          <h3 className="text-sm font-medium text-white">
            用户标签页管理
          </h3>
          {userTabsCount > 0 && (
            <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
              {userTabsCount}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-slate-400" />
          ) : (
            <ChevronDown className="w-4 h-4 text-slate-400" />
          )}
        </div>
      </button>

      {/* 可展开内容 */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-80 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        {isExpanded && (
          <div className="px-4 pb-4">
            <div className="border-t border-slate-700 pt-4">
              <UserTabsManager />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollapsibleUserTabsManager;
