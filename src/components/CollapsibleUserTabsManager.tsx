import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, MoreHorizontal } from 'lucide-react';
import UserTabsManager from '@/components/UserTabsManager';

interface CollapsibleUserTabsManagerProps {
  className?: string;
}

/**
 * 可展开/收起的用户标签页管理组件
 * 职责：提供可折叠的用户标签页管理界面
 */
const CollapsibleUserTabsManager: React.FC<CollapsibleUserTabsManagerProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [userTabsCount, setUserTabsCount] = useState<number>(0);
  const [isBatchMode, setIsBatchMode] = useState<boolean>(false);

  /**
   * 加载展开状态
   */
  useEffect(() => {
    const loadExpandedState = async () => {
      try {
        const result = await chrome.storage.local.get(['userTabsManagerExpanded']);
        setIsExpanded(result.userTabsManagerExpanded || false);
      } catch (error) {
        console.error('加载用户标签页管理展开状态失败:', error);
      }
    };

    loadExpandedState();
  }, []);

  /**
   * 保存展开状态
   */
  const saveExpandedState = async (expanded: boolean) => {
    try {
      await chrome.storage.local.set({ userTabsManagerExpanded: expanded });
    } catch (error) {
      console.error('保存用户标签页管理展开状态失败:', error);
    }
  };

  /**
   * 切换展开/收起状态
   */
  const toggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    saveExpandedState(newExpanded);
  };

  /**
   * 获取用户标签页数量（使用精确的用户标签页判断逻辑）
   */
  const getUserTabsCount = async () => {
    try {
      // 获取当前窗口的标签页（只统计当前窗口的用户标签页）
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 使用精确的用户标签页判断逻辑
      const { UserTabsUtils } = await import('@/utils/tabs');
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      let count = 0;

      for (const tab of allTabs) {
        if (!tab.url || !tab.id) continue;

        // 排除系统页面
        if (UserTabsUtils.isSystemTab(tab.url) || UserTabsUtils.isExtensionTab(tab.url)) {
          continue;
        }

        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有Workona ID映射的非系统标签页是用户标签页
          count++;
          continue;
        }

        // 检查是否为工作区核心标签页
        const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
        if (metadataResult.success && metadataResult.data) {
          const { isWorkspaceCore } = metadataResult.data;
          if (!isWorkspaceCore) {
            // 非工作区核心标签页（会话临时标签页）是用户标签页
            count++;
          }
        } else {
          // 映射损坏的标签页也算作用户标签页
          count++;
        }
      }

      setUserTabsCount(count);
    } catch (error) {
      console.error('获取用户标签页数量失败:', error);
      setUserTabsCount(0);
    }
  };

  /**
   * 初始化和实时监控标签页数量变化
   */
  useEffect(() => {
    getUserTabsCount();

    // 监听标签页变化事件
    const handleTabsChanged = () => {
      getUserTabsCount();
    };

    // 监听Chrome标签页事件
    if (chrome.tabs) {
      chrome.tabs.onCreated.addListener(handleTabsChanged);
      chrome.tabs.onRemoved.addListener(handleTabsChanged);
      chrome.tabs.onUpdated.addListener(handleTabsChanged);
    }

    // 监听窗口变化事件
    if (chrome.windows) {
      chrome.windows.onCreated.addListener(handleTabsChanged);
      chrome.windows.onRemoved.addListener(handleTabsChanged);
    }

    // 监听Workona ID映射变化（用于"添加当前标签页"后的状态更新）
    const handleStorageChanged = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes['user_tabs_refresh_trigger']) {
        console.log('🔄 检测到用户标签页状态变化，更新数量显示');
        getUserTabsCount();
      }
    };

    if (chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.addListener(handleStorageChanged);
    }

    // 定期更新作为备用机制
    const interval = setInterval(getUserTabsCount, 10000);

    return () => {
      clearInterval(interval);
      // 清理事件监听器
      if (chrome.tabs) {
        chrome.tabs.onCreated.removeListener(handleTabsChanged);
        chrome.tabs.onRemoved.removeListener(handleTabsChanged);
        chrome.tabs.onUpdated.removeListener(handleTabsChanged);
      }
      if (chrome.windows) {
        chrome.windows.onCreated.removeListener(handleTabsChanged);
        chrome.windows.onRemoved.removeListener(handleTabsChanged);
      }
      if (chrome.storage && chrome.storage.onChanged) {
        chrome.storage.onChanged.removeListener(handleStorageChanged);
      }
    };
  }, []);

  return (
    <div className={`workspace-item ${className}`}>
      {/* 工作区头部 - 左对齐充满布局 */}
      <div className="flex items-center w-full">
        <div
          className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer"
          onClick={toggleExpanded}
        >
          {/* 展开/折叠按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleExpanded();
            }}
            className="p-1 hover:bg-slate-600 rounded transition-colors duration-150 flex-shrink-0"
          >
            {isExpanded ? (
              <ChevronDown className="w-3.5 h-3.5 text-slate-400" />
            ) : (
              <ChevronRight className="w-3.5 h-3.5 text-slate-400" />
            )}
          </button>

          {/* 用户标签页管理图标 */}
          <div
            className="w-7 h-7 rounded flex items-center justify-center text-base flex-shrink-0"
            style={{ backgroundColor: 'rgba(59, 130, 246, 0.125)', color: 'rgb(59, 130, 246)' }}
          >
            📋
          </div>

          {/* 标题信息 - 充满剩余空间 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-white truncate text-sm">
                用户标签页管理
              </h3>
            </div>
            <div className="flex items-center gap-2 text-xs text-slate-400">
              <span>{userTabsCount} 个标签页</span>
            </div>
          </div>
        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center gap-1">
          {/* 占位区域 */}
          <div className="flex items-center gap-2"></div>

          {/* 批量操作按钮 */}
          <div className="flex items-center gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsBatchMode(!isBatchMode);
              }}
              className="p-1 hover:bg-slate-700 rounded transition-colors duration-200"
              title="批量操作"
            >
              <MoreHorizontal className="w-4 h-4 text-slate-400" />
            </button>
          </div>
        </div>
      </div>

      {/* 用户标签页列表 */}
      {isExpanded && (
        <div className="mt-2">
          <UserTabsManager
            isBatchMode={isBatchMode}
            onBatchModeChange={setIsBatchMode}
          />
        </div>
      )}
    </div>
  );
};

export default CollapsibleUserTabsManager;
