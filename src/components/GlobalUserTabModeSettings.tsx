import React, { useState, useEffect } from 'react';
import { StorageManager } from '@/utils/storage';
import { useToast } from '@/components/Toast';

interface GlobalUserTabModeSettingsProps {
  className?: string;
}

/**
 * 全局用户标签模式设置组件
 * 职责：管理全局用户标签模式的开关设置
 */
const GlobalUserTabModeSettings: React.FC<GlobalUserTabModeSettingsProps> = ({ className = '' }) => {
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { showError, showSuccess } = useToast();

  /**
   * 加载当前设置
   */
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const result = await StorageManager.getSettings();
        if (result.success) {
          setIsEnabled(result.data!.globalUserTabMode);
        } else {
          console.error('加载设置失败:', result.error);
          showError('加载设置失败');
        }
      } catch (error) {
        console.error('加载设置时出错:', error);
        showError('加载设置时出错');
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [showError]);

  /**
   * 处理设置变更
   */
  const handleToggle = async () => {
    if (isLoading) return;

    const newValue = !isEnabled;
    setIsLoading(true);

    try {
      const result = await StorageManager.saveSettings({
        globalUserTabMode: newValue
      });

      if (result.success) {
        setIsEnabled(newValue);
        showSuccess(newValue ? '已启用全局用户标签模式' : '已禁用全局用户标签模式');
        console.log(`✅ 全局用户标签模式已${newValue ? '启用' : '禁用'}`);
      } else {
        console.error('保存设置失败:', result.error);
        showError('保存设置失败');
      }
    } catch (error) {
      console.error('保存设置时出错:', error);
      showError('保存设置时出错');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 设置项标题和描述 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-white">
              全局用户标签模式
            </h3>
            <p className="text-xs text-slate-400 mt-1">
              启用后，用户标签将在所有工作区间共享，不再受工作区隔离限制
            </p>
          </div>
          
          {/* Toggle Switch */}
          <button
            onClick={handleToggle}
            disabled={isLoading}
            className={`
              relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
              ${isEnabled 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-slate-600 hover:bg-slate-500'
              }
              ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800
            `}
            role="switch"
            aria-checked={isEnabled}
            aria-label="切换全局用户标签模式"
          >
            <span
              className={`
                inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
                ${isEnabled ? 'translate-x-6' : 'translate-x-1'}
              `}
            />
          </button>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="flex items-center space-x-2 text-xs">
        <div className={`
          w-2 h-2 rounded-full
          ${isEnabled ? 'bg-green-400' : 'bg-slate-500'}
        `} />
        <span className="text-slate-400">
          {isLoading ? '设置更新中...' : (isEnabled ? '已启用' : '已禁用')}
        </span>
      </div>
    </div>
  );
};

export default GlobalUserTabModeSettings;
