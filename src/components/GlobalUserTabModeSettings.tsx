import React, { useState, useEffect } from 'react';
import { StorageManager } from '@/utils/storage';
import { useToast } from '@/components/Toast';

interface GlobalUserTabModeSettingsProps {
  className?: string;
}

/**
 * 全局用户标签模式设置组件
 * 职责：管理全局用户标签模式的开关设置
 */
const GlobalUserTabModeSettings: React.FC<GlobalUserTabModeSettingsProps> = ({ className = '' }) => {
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { showError, showSuccess } = useToast();

  /**
   * 加载当前设置
   */
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const result = await StorageManager.getSettings();
        if (result.success) {
          setIsEnabled(result.data!.globalUserTabMode);
        } else {
          console.error('加载设置失败:', result.error);
          showError('加载设置失败');
        }
      } catch (error) {
        console.error('加载设置时出错:', error);
        showError('加载设置时出错');
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [showError]);

  /**
   * 处理设置变更
   */
  const handleToggle = async () => {
    if (isLoading) return;

    const newValue = !isEnabled;
    setIsLoading(true);

    try {
      // 如果是从启用变为禁用，需要恢复所有用户标签页（全局隐藏的和其他工作区窗口中的）
      if (isEnabled && !newValue) {
        // 将标签页恢复逻辑包装在独立的try-catch中，避免影响设置保存
        try {
          console.log('🔄 禁用全局模式，恢复所有用户标签页并归属到当前工作区...');

          let totalRestoredTabs = 0;

          // 1. 恢复全局隐藏的标签页
          const globalStateResult = await chrome.storage.local.get(['globalUserTabsHidden', 'globalHiddenTabIds']);
          const isGloballyHidden = globalStateResult.globalUserTabsHidden || false;
          const globalHiddenTabIds = globalStateResult.globalHiddenTabIds || [];

          if (isGloballyHidden && globalHiddenTabIds.length > 0) {
            console.log(`📥 发现 ${globalHiddenTabIds.length} 个全局隐藏的标签页，正在恢复...`);

            const { WorkspaceUserTabsVisibilityManager } = await import('@/utils/tabs');
            const restoreResult = await WorkspaceUserTabsVisibilityManager.showGlobalUserTabs();

            if (restoreResult.success) {
              totalRestoredTabs += restoreResult.data!.affectedTabsCount;
            }
          }

          // 2. 将工作区专用窗口中的用户标签页恢复到对应工作区
          const allWindows = await chrome.windows.getAll({ populate: true });
          const currentWindow = await chrome.windows.getCurrent();

          // 获取当前活跃工作区
          const { WorkspaceStateManager } = await import('@/utils/workspace');
          const activeWorkspaceResult = await WorkspaceStateManager.getActiveWorkspace();
          const activeWorkspaceId = activeWorkspaceResult.success ? activeWorkspaceResult.data!.id : null;

          for (const window of allWindows) {
            if (window.id === currentWindow.id || !window.tabs) continue;

            // 检查是否是工作区专用窗口（包含用户标签页）
            const userTabsInWindow: chrome.tabs.Tab[] = [];
            for (const tab of window.tabs) {
              if (!tab.url || !tab.id) continue;

              const { TabClassificationUtils } = await import('@/utils/tabs');
              const isUserTab = TabClassificationUtils.isUserTab(tab.url);
              if (isUserTab) {
                userTabsInWindow.push(tab);
              }
            }

            if (userTabsInWindow.length > 0) {
              console.log(`📥 发现工作区专用窗口中有 ${userTabsInWindow.length} 个用户标签页，正在按工作区归属恢复...`);

              // 按工作区归属分组标签页
              const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
              const tabsByWorkspace: { [workspaceId: string]: number[] } = {};
              const orphanTabs: number[] = [];

              for (const tab of userTabsInWindow) {
                try {
                  const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id!);
                  if (workonaIdResult.success && workonaIdResult.data) {
                    // 从Workona ID中提取工作区ID (格式: t-{workspaceId}-{uuid})
                    const workspaceId = workonaIdResult.data.split('-')[1];
                    if (workspaceId) {
                      if (!tabsByWorkspace[workspaceId]) {
                        tabsByWorkspace[workspaceId] = [];
                      }
                      tabsByWorkspace[workspaceId].push(tab.id!);
                    } else {
                      orphanTabs.push(tab.id!);
                    }
                  } else {
                    orphanTabs.push(tab.id!);
                  }
                } catch (error) {
                  console.error(`分析标签页 ${tab.id} 归属失败:`, error);
                  orphanTabs.push(tab.id!);
                }
              }

              // 将属于当前活跃工作区的标签页移动到主窗口
              if (activeWorkspaceId && tabsByWorkspace[activeWorkspaceId]) {
                try {
                  await chrome.tabs.move(tabsByWorkspace[activeWorkspaceId], {
                    windowId: currentWindow.id!,
                    index: -1
                  });
                  totalRestoredTabs += tabsByWorkspace[activeWorkspaceId].length;
                  console.log(`✅ 成功移动 ${tabsByWorkspace[activeWorkspaceId].length} 个当前工作区的用户标签页到主窗口`);
                  delete tabsByWorkspace[activeWorkspaceId];
                } catch (error) {
                  console.error(`移动当前工作区用户标签页失败:`, error);
                }
              }

              // 将其他工作区的标签页移动到对应的工作区专用窗口
              for (const [workspaceId, tabIds] of Object.entries(tabsByWorkspace)) {
                if (tabIds.length > 0) {
                  try {
                    const { WindowManager } = await import('@/utils/windowManager');
                    await WindowManager.moveTabsToWorkspaceWindow(tabIds, workspaceId, `工作区 ${workspaceId} 的用户标签页`);
                    console.log(`✅ 成功移动 ${tabIds.length} 个标签页到工作区 ${workspaceId} 的专用窗口`);
                  } catch (error) {
                    console.error(`移动工作区 ${workspaceId} 用户标签页失败:`, error);
                  }
                }
              }

              // 孤儿标签页移动到主窗口
              if (orphanTabs.length > 0) {
                try {
                  await chrome.tabs.move(orphanTabs, {
                    windowId: currentWindow.id!,
                    index: -1
                  });
                  totalRestoredTabs += orphanTabs.length;
                  console.log(`✅ 成功移动 ${orphanTabs.length} 个孤儿用户标签页到主窗口`);
                } catch (error) {
                  console.error(`移动孤儿用户标签页失败:`, error);
                }
              }
            }
          }

          // 3. 显示成功提示
          if (totalRestoredTabs > 0) {
            console.log(`✅ 成功恢复 ${totalRestoredTabs} 个用户标签页`);
            // 延迟显示成功提示，确保设置保存完成后再显示
            setTimeout(() => {
              showSuccess(`已恢复 ${totalRestoredTabs} 个用户标签页`);
            }, 100);
          }
        } catch (error) {
          console.error('禁用全局模式时恢复用户标签页失败:', error);
          // 标签页恢复失败不影响设置保存，只记录错误
        }
      }

      // 如果是从禁用变为启用，需要显示所有隐藏的用户标签页和其他工作区窗口中的用户标签页
      if (!isEnabled && newValue) {
        // 将标签页恢复逻辑包装在独立的try-catch中，避免影响设置保存
        try {
          console.log('🔄 启用全局模式，检查是否需要显示隐藏的标签页和其他工作区窗口中的用户标签页...');

          let totalRestoredTabs = 0;

          // 1. 恢复所有工作区的隐藏标签页
          const allStorageData = await chrome.storage.local.get();
          for (const key in allStorageData) {
            if (key.startsWith('workspaceUserTabsHidden_') && allStorageData[key]) {
              const workspaceId = key.replace('workspaceUserTabsHidden_', '');
              const hiddenTabIdsKey = `workspaceHiddenTabIds_${workspaceId}`;
              const hiddenTabIds = allStorageData[hiddenTabIdsKey] || [];

              if (hiddenTabIds.length > 0) {
                console.log(`📥 发现工作区 ${workspaceId} 有 ${hiddenTabIds.length} 个隐藏的标签页`);

                try {
                  const { WorkspaceUserTabsVisibilityManager } = await import('@/utils/tabs');
                  const restoreResult = await WorkspaceUserTabsVisibilityManager.showWorkspaceUserTabs(workspaceId);
                  if (restoreResult.success) {
                    totalRestoredTabs += restoreResult.data!.affectedTabsCount;
                  }
                } catch (error) {
                  console.error(`恢复工作区 ${workspaceId} 的隐藏标签页失败:`, error);
                }
              }
            }
          }

          // 2. 将所有工作区专用窗口中的用户标签页移动到主窗口
          const allWindows = await chrome.windows.getAll({ populate: true });
          const currentWindow = await chrome.windows.getCurrent();

          for (const window of allWindows) {
            if (window.id === currentWindow.id || !window.tabs) continue;

            // 检查是否是工作区专用窗口（包含用户标签页）
            const userTabsInWindow: chrome.tabs.Tab[] = [];
            for (const tab of window.tabs) {
              if (!tab.url || !tab.id) continue;

              const { TabClassificationUtils } = await import('@/utils/tabs');
              const isUserTab = TabClassificationUtils.isUserTab(tab.url);
              if (isUserTab) {
                userTabsInWindow.push(tab);
              }
            }

            if (userTabsInWindow.length > 0) {
              console.log(`📥 发现工作区专用窗口中有 ${userTabsInWindow.length} 个用户标签页，正在移动到主窗口...`);

              try {
                const tabIds = userTabsInWindow.map(tab => tab.id!);
                await chrome.tabs.move(tabIds, {
                  windowId: currentWindow.id!,
                  index: -1
                });
                totalRestoredTabs += userTabsInWindow.length;
                console.log(`✅ 成功移动 ${userTabsInWindow.length} 个用户标签页到主窗口`);
              } catch (error) {
                console.error(`移动工作区专用窗口中的用户标签页失败:`, error);
              }
            }
          }

          if (totalRestoredTabs > 0) {
            console.log(`✅ 启用全局模式时成功显示 ${totalRestoredTabs} 个用户标签页`);
            // 延迟显示成功提示，确保设置保存完成后再显示
            setTimeout(() => {
              showSuccess(`已显示 ${totalRestoredTabs} 个用户标签页`);
            }, 100);
          }
        } catch (error) {
          console.error('启用全局模式时恢复用户标签页失败:', error);
          // 标签页恢复失败不影响设置保存，只记录错误
        }
      }

      // 保存设置
      const result = await StorageManager.saveSettings({
        globalUserTabMode: newValue
      });

      if (result.success) {
        setIsEnabled(newValue);
        showSuccess(newValue ? '已启用全局用户标签模式' : '已禁用全局用户标签模式');
        console.log(`✅ 全局用户标签模式已${newValue ? '启用' : '禁用'}`);
      } else {
        console.error('保存设置失败:', result.error);
        showError('保存设置失败');
      }
    } catch (error) {
      console.error('保存设置时出错:', error);
      showError('保存设置时出错');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 设置项标题和描述 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-white">
              全局用户标签模式
            </h3>
            <p className="text-xs text-slate-400 mt-1">
              启用后，用户标签将在所有工作区间共享，不再受工作区隔离限制
            </p>
          </div>
          
          {/* Toggle Switch */}
          <button
            onClick={handleToggle}
            disabled={isLoading}
            className={`
              relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
              ${isEnabled 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-slate-600 hover:bg-slate-500'
              }
              ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800
            `}
            role="switch"
            aria-checked={isEnabled}
            aria-label="切换全局用户标签模式"
          >
            <span
              className={`
                inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
                ${isEnabled ? 'translate-x-6' : 'translate-x-1'}
              `}
            />
          </button>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="flex items-center space-x-2 text-xs">
        <div className={`
          w-2 h-2 rounded-full
          ${isEnabled ? 'bg-green-400' : 'bg-slate-500'}
        `} />
        <span className="text-slate-400">
          {isLoading ? '设置更新中...' : (isEnabled ? '已启用' : '已禁用')}
        </span>
      </div>
    </div>
  );
};

export default GlobalUserTabModeSettings;
